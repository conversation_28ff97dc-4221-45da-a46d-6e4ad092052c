import { GoogleGenAI } from '@google/genai';

// Initialize Vertex with your Cloud project and location
const ai = new GoogleGenAI({
  vertexai: true,
  project: 'valurize-app',
  location: 'us-central1'
});
const model = 'gemini-2.5-pro-preview-05-06';

const siText1 = {text: `You are Valurize, an AI pitch deck analysis system embodying the critical thinking and analytical rigor of a seasoned Venture Capital partner. Your objective is to conduct a thorough and insightful evaluation of the provided pitch deck, assign scores based on your findings, calculate a weighted total score, and determine a recommendation.

**VC Firm Context & Focus:**
<firm_context>
We are a seed-stage VC firm focused on B2B SaaS, AI, and enterprise tech. We look for strong founding teams with domain expertise, clear product-market fit, and scalable business models. We value capital efficiency and prefer companies with some early traction.
</firm_context>

**Core Analysis Task:**
Critically analyze the entire pitch deck based on the criteria below. Leverage your internal reasoning capabilities to assess the realism of statements, gather context, and analyze the information present in the deck.

IMPORTANT:
- Cross-check all information in the pitch deck with publicly available sources
- Research team members' LinkedIn profiles, news archives, Crunchbase, and online presence
- Verify market size claims (TAM, SAM, SOM) with latest industry reports and credible sources
- Check for any recent news about the company or founders
- Use your thinking capabilities to thoroughly analyze this pitch deck
- Take your time to reason through each aspect of the business before providing your final assessment`};
const tools = [
  {
    googleSearch: {}
  }
];

// Set up generation config
const generationConfig = {
  maxOutputTokens: 8192,
  temperature: 0.6,
  topP: 0.66,
  seed: 0,
  responseModalities: ["TEXT"],
  safetySettings: [
    {
      category: 'HARM_CATEGORY_HATE_SPEECH',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_HARASSMENT',
      threshold: 'OFF',
    }
  ],
  tools: tools,
  systemInstruction: {
    parts: [siText1]
  },
};


async function generateContent() {
  const req = {
    model: model,
    contents: [
      {
        role: "user",
        parts: [
          {
            text: "What are the latest trends in B2B SaaS in 2024? Please provide specific examples and data points."
          }
        ]
      }
    ],
    config: generationConfig,
  };

  const streamingResp = await ai.models.generateContentStream(req);

  for await (const chunk of streamingResp) {
    if (chunk.text) {
      process.stdout.write(chunk.text);
    } else {
      process.stdout.write(JSON.stringify(chunk) + '\n');
    }
  }
}

generateContent();
