const PDFDocument = require('pdfkit');
const fs = require('fs');

// Create a document
const doc = new PDFDocument();

// Pipe its output somewhere, like to a file
doc.pipe(fs.createWriteStream('sample.pdf'));

// Add content to the PDF
doc.fontSize(25).text('Sample Pitch Deck', {
  align: 'center'
});

doc.moveDown();
doc.fontSize(18).text('TechStartup Inc.', {
  align: 'center'
});

doc.moveDown();
doc.fontSize(12).text('Executive Summary', {
  underline: true
});
doc.fontSize(10).text(
  'TechStartup Inc. is a revolutionary AI-powered platform that helps businesses ' +
  'optimize their operations through advanced machine learning algorithms. ' +
  'Our solution reduces operational costs by 30% while increasing productivity by 25%.'
);

doc.moveDown();
doc.fontSize(12).text('The Team', {
  underline: true
});
doc.fontSize(10).text(
  'Our founding team consists of industry veterans with over 30 years of combined experience:\n' +
  '- <PERSON>, CEO - Former VP at Google\n' +
  '- <PERSON>, CTO - MIT PhD in Computer Science\n' +
  '- <PERSON>, COO - Former operations director at Amazon'
);

doc.moveDown();
doc.fontSize(12).text('Market Opportunity', {
  underline: true
});
doc.fontSize(10).text(
  'The global AI market is projected to reach $190 billion by 2025, growing at a CAGR of 37%. ' +
  'Our target segment within this market is valued at $45 billion with limited competition.'
);

doc.moveDown();
doc.fontSize(12).text('Product', {
  underline: true
});
doc.fontSize(10).text(
  'Our AI platform integrates seamlessly with existing enterprise systems and provides ' +
  'real-time insights and automation capabilities. Key features include:\n' +
  '- Predictive analytics\n' +
  '- Process automation\n' +
  '- Custom AI models'
);

doc.moveDown();
doc.fontSize(12).text('Traction', {
  underline: true
});
doc.fontSize(10).text(
  'We have secured 5 pilot customers including two Fortune 500 companies. ' +
  'Current ARR is $500,000 with a projected growth to $2M by end of year.'
);

doc.moveDown();
doc.fontSize(12).text('Business Model', {
  underline: true
});
doc.fontSize(10).text(
  'We operate on a SaaS model with tiered pricing:\n' +
  '- Basic: $1,000/month\n' +
  '- Professional: $5,000/month\n' +
  '- Enterprise: $20,000/month\n\n' +
  'Average contract value is $60,000 with 85% gross margins.'
);

doc.moveDown();
doc.fontSize(12).text('Financials', {
  underline: true
});
doc.fontSize(10).text(
  'Year 1: $500K revenue, $1.2M expenses\n' +
  'Year 2: $2M revenue, $2.5M expenses\n' +
  'Year 3: $8M revenue, $5M expenses\n\n' +
  'We are raising $5M to fund our growth for the next 18 months.'
);

doc.moveDown();
doc.fontSize(12).text('Investment Opportunity', {
  underline: true
});
doc.fontSize(10).text(
  'We are seeking $5M in Series A funding to accelerate our growth. ' +
  'The funds will be used for:\n' +
  '- Product development (40%)\n' +
  '- Sales and marketing (35%)\n' +
  '- Operations (25%)'
);

// Finalize PDF file
doc.end();
