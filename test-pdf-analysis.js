import { GoogleGenAI } from '@google/genai';
import * as fs from 'fs';
import * as path from 'path';

// Initialize Vertex with your Cloud project and location
const ai = new GoogleGenAI({
  vertexai: true,
  project: 'valurize-app',
  location: 'us-central1'
});
const model = 'gemini-2.5-pro-preview-05-06';

// Sample VC firm context
const prompt = `We are a seed-stage VC firm focused on B2B SaaS, AI, and enterprise tech. We look for strong founding teams with domain expertise, clear product-market fit, and scalable business models. We value capital efficiency and prefer companies with some early traction.`;

const siText1 = {text: `You are Valurize, an AI pitch deck analysis system embodying the critical thinking and analytical rigor of a seasoned Venture Capital partner. Your objective is to conduct a thorough and insightful evaluation of the provided pitch deck, assign scores based on your findings, calculate a weighted total score, and determine a recommendation.

**VC Firm Context & Focus:**
<firm_context>
${prompt}
</firm_context>

**Core Analysis Task:**
Critically analyze the entire pitch deck based on the criteria below. Leverage your internal reasoning capabilities to assess the realism of statements, gather context, and analyze the information present in the deck.

IMPORTANT:
- Cross-check all information in the pitch deck with publicly available sources
- Research team members' LinkedIn profiles, news archives, Crunchbase, and online presence
- Verify market size claims (TAM, SAM, SOM) with latest industry reports and credible sources
- Check for any recent news about the company or founders
- Use your thinking capabilities to thoroughly analyze this pitch deck
- Take your time to reason through each aspect of the business before providing your final assessment

**Evaluation Criteria & Scoring (Assign 1-5 for each):**
(1: Significant Weakness/Red Flag, 2: Weakness, 3: Average/Adequate, 4: Strength, 5: Exceptional Strength)

1. **Team (Weight: 25%):**
   - Evaluate the founding team's experience, domain expertise, and track record
   - Verify backgrounds using LinkedIn, news archives, Crunchbase
   - Assess relevant experience, domain expertise, completeness
   - Determine if any founder has a verified prior successful exit

2. **Market (Weight: 20%):**
   - Assess market size, growth potential, and competitive landscape
   - Verify claimed TAM, SAM, and SOM against independent market research
   - Evaluate market growth trends and dynamics
   - Identify the primary industry/sector

3. **Product (Weight: 20%):**
   - Evaluate product-market fit, innovation, and technical feasibility
   - Research similar products/services in the market
   - Assess technological feasibility and implementation challenges
   - Evaluate intellectual property position and defensibility

4. **Traction (Weight: 15%):**
   - Analyze current customers, revenue, and growth metrics
   - Substantiate claimed milestones using external validation
   - Differentiate meaningful progress from vanity metrics
   - Assess stage-appropriateness

5. **Business Model (Weight: 10%):**
   - Evaluate revenue model, unit economics, and scalability
   - Compare with industry benchmarks
   - Assess pricing strategy against competitors
   - Research customer acquisition costs and lifetime value benchmarks

6. **Financials (Weight: 10%):**
   - Assess financial projections, funding needs, and use of funds
   - Evaluate the realism of forecasts and underlying assumptions
   - Compare key metrics against industry benchmarks
   - Assess capital efficiency and runway implications

7. **Outlier Potential (Weight: 5%):**
   - Look for signals of a potential high-risk/high-reward outlier
   - Assess if targeting a non-obvious problem/market
   - Evaluate for highly unconventional approach
   - Check for evidence of intense early user love in a niche
   - Look for potential for market creation
   - Assess for exceptional founder vision/obsession that deviates from norms

**Weighted Score Calculation:**
Calculate the Total Score by summing the weighted scores (Score * Weight) for all criteria. The maximum possible score is 5.0.

**Recommendation Logic:**
Based on the calculated Total Score:
- High Priority (Invest): Total Score >= 4.0
- Maybe (Further Review): Total Score >= 3.0 and < 4.0
- Pass: Total Score < 3.0

**Output Format:**
1. **Detailed Memo:**
   - **Executive Summary:** Key strengths, critical weaknesses, outlier potential note.
   - **Scoring & Analysis:** For each criterion, state the score (1–5), rationale, and citations.
   - **Outlier Assessment:** Score and justification.
   - **Red Flag Summary:** 1–2 sentence watchouts.
   - **Key Diligence Questions:** Actionable questions for founders.
2. **Notion & Slack Payloads:** Populate JSON objects matching your existing \\\`notion_payload\\\` and \\\`slack_payload\\\` schemas.
3. **Final Confirmation:** End with \\\`Analysis processing complete.\\\`

**Behavioral Rules:**
- Use grounded web search calls when verifying claims.
- Leverage chain-of-thought for deep analysis.
- Be concise, specific, and avoid fluff.
- Always cite sources for external data.
- Delete any sensitive or PII from logs and outputs.

Provide your response in the following JSON format:
{
  "companyName": "Name of the company",
  "score": <Calculated Total Weighted Score, >,
  "summary": "Brief executive summary of the company and opportunity",
  "strengths": ["Strength 1", "Strength 2", ...],
  "weaknesses": ["Weakness 1", "Weakness 2", ...],
  "marketOpportunity": {
    "size": "Market size assessment",
    "growth": "Market growth assessment",
    "assessment": "Overall market opportunity assessment",
    "sources": ["Source 1", "Source 2", ...]
  },
  "team": {
    "assessment": "Overall team assessment",
    "experience": "Team experience assessment",
    "gaps": "Identified team gaps",
    "sources": ["Source 1", "Source 2", ...]
  },
  "financials": {
    "assessment": "Overall financial assessment",
    "runway": "Runway assessment",
    "projections": "Projection realism assessment",
    "sources": ["Source 1", "Source 2", ...]
  },
  "outlierPotential": "Assessment of potential for high-risk/high-reward outlier success",
  "investmentRecommendation": "Clear recommendation based on the logic above (High Priority, Maybe, Pass)",
  "followUpQuestions": ["Question 1", "Question 2", ...],
  "sources": {
    "general": ["Source 1", "Source 2", ...],
    "industry": ["Source 1", "Source 2", ..."],
    "competitors": ["Source 1", "Source 2", ..."]
  },
  "detailedMemo": "Full detailed analysis memo with all sections",
  "industrySector": ["Primary Industry", "Secondary Industry"],
  "founderPriorExit": true/false,
  "teamScore": <Score out of 5.0>,
  "marketScore": <Score out of 5.0>,
  "tractionScore": <Score out of 5.0>,
  "outlierPotentialScore": <Score out of 5.0>,
  "oneSentenceDescription": "A single sentence description of the company",
  "keyWatchouts": ["Watchout 1", "Watchout 2", "Watchout 3"],
  "notionData": {
    "startup_name": "Name of the company",
    "one_sentence_description": "A single sentence description of the company",
    "analysis_date": "Current date",
    "overall_score": <Score out of 5.0>,
    "recommendation": "High Priority, Maybe, or Pass",
    "industry_sector": ["Primary Industry", "Secondary Industry"],
    "founder_prior_exit": true/false,
    "team_score": <Score out of 5.0>,
    "market_score": <Score out of 5.0>,
    "traction_score": <Score out of 5.0>,
    "outlier_potential_score": <Score out of 5.0>,
    "key_watchouts": "• Watchout 1\\\\n• Watchout 2\\\\n• Watchout 3"
  }
}`};

// Enable Google Search grounding
const tools = [
  {
    googleSearch: {}
  }
];

// Set up generation config
const generationConfig = {
  maxOutputTokens: 8192,
  temperature: 0.6,
  topP: 0.66,
  seed: 0,
  responseModalities: ["TEXT"],
  safetySettings: [
    {
      category: 'HARM_CATEGORY_HATE_SPEECH',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_HARASSMENT',
      threshold: 'OFF',
    }
  ],
  tools: tools,
  systemInstruction: {
    parts: [siText1]
  },
};

// Path to a sample PDF file
const pdfPath = './test/Airbnb.pdf';

async function analyzePDF() {
  try {
    // Read the PDF file
    const pdfBuffer = fs.readFileSync(pdfPath);
    const base64PdfString = pdfBuffer.toString('base64');

    console.log(`Analyzing PDF: ${pdfPath}`);

    const req = {
      model: model,
      contents: [
        {
          role: "user",
          parts: [
            {
              text: "Please analyze this pitch deck:"
            },
            {
              inlineData: {
                mimeType: 'application/pdf',
                data: base64PdfString
              }
            }
          ]
        }
      ],
      config: generationConfig,
    };

    console.log('Generating content with Gemini 2.5 Pro Preview...');

    // Use non-streaming API for PDF analysis to get the complete response
    // Using 'as any' to bypass TypeScript type checking
    const response = await ai.models.generateContent(req);

    console.log('\nResponse:');
    console.log('=========');
    console.log(response.text);

    // Process the response similar to geminiService.ts
    console.log('\nProcessing response to match geminiService.ts format...');

    // Extract the text from the response
    const responseText = response.text;

    if (!responseText) {
      throw new Error('No text found in Gemini response');
    }

    // Extract the JSON from the response
    // First try to find JSON between ```json and ``` markers
    let jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);

    if (!jsonMatch) {
      // If not found, try to find any JSON object
      jsonMatch = responseText.match(/({[\s\S]*})/);
    }

    if (!jsonMatch) {
      throw new Error('No JSON found in Gemini response');
    }

    // Clean up the JSON string to handle potential issues
    let jsonString = jsonMatch[1];

    // Try to parse the JSON, handling any errors
    let rawAnalysis;
    try {
      // First attempt: direct parsing
      rawAnalysis = JSON.parse(jsonString);
      console.log('Successfully parsed JSON response');
    } catch (error) {
      console.error(`Error parsing JSON: ${error.message}`);
      console.log('Attempting to clean the JSON string...');

      // Second attempt: clean the JSON string
      try {
        // Fix common JSON issues
        const cleanedJson = jsonString
          .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
          .replace(/\\(?!["\\/bfnrt])/g, '\\\\') // Escape backslashes
          .replace(/\n/g, '\\n') // Handle newlines
          .replace(/\r/g, '\\r') // Handle carriage returns
          .replace(/\t/g, '\\t') // Handle tabs
          .replace(/([^\\])\\([^"\\/bfnrtu])/g, '$1\\\\$2') // Fix invalid escapes
          .replace(/([^\\])\\'/g, "$1\\\\'") // Fix single quotes
          .replace(/\t/g, '\\t'); // Handle tabs

        rawAnalysis = JSON.parse(cleanedJson);
        console.log('Successfully parsed JSON after cleaning');
      } catch (cleanError) {
        console.error(`Still unable to parse JSON after cleaning: ${cleanError.message}`);

        // Third attempt: Use a more lenient JSON parser
        try {
          // Use Function constructor as a last resort (be careful with this in production)
          rawAnalysis = (new Function('return ' + jsonString))();
          console.log('Successfully parsed JSON using Function constructor');
        } catch (lenientError) {
          console.error(`Failed to parse JSON with lenient parser: ${lenientError.message}`);

          // Fourth attempt: Extract directly from the response text
          console.log('Attempting to extract JSON properties directly from response text...');

          // Extract key fields using regex
          const companyNameMatch = responseText.match(/"companyName"\s*:\s*"([^"]+)"/);
          const scoreMatch = responseText.match(/"score"\s*:\s*(\d+)/);
          const summaryMatch = responseText.match(/"summary"\s*:\s*"([^"]+)"/);
          const recommendationMatch = responseText.match(/"investmentRecommendation"\s*:\s*"([^"]+)"/);

          if (companyNameMatch && scoreMatch) {
            console.log('Successfully extracted basic properties from response text');

            // Extract arrays
            const extractArray = (text, fieldName) => {
              const regex = new RegExp(`"${fieldName}"\\s*:\\s*\\[(.*?)\\]`, 's');
              const match = text.match(regex);
              if (!match) return [];

              // Simple extraction of quoted strings
              const items = [];
              const itemRegex = /"([^"]+)"/g;
              let itemMatch;

              while ((itemMatch = itemRegex.exec(match[1])) !== null) {
                items.push(itemMatch[1]);
              }

              return items;
            };

            // Extract nested objects
            const extractNestedObject = (text, objectName) => {
              const regex = new RegExp(`"${objectName}"\\s*:\\s*{([^}]+)}`, 's');
              const match = text.match(regex);
              if (!match) return null;

              const result = {};

              // Extract simple key-value pairs
              const keyValueRegex = /"([^"]+)"\s*:\s*"([^"]+)"/g;
              let keyValueMatch;
              while ((keyValueMatch = keyValueRegex.exec(match[1])) !== null) {
                result[keyValueMatch[1]] = keyValueMatch[2];
              }

              // Extract numeric values
              const numericRegex = /"([^"]+)"\s*:\s*(\d+(\.\d+)?)/g;
              let numericMatch;
              while ((numericMatch = numericRegex.exec(match[1])) !== null) {
                result[numericMatch[1]] = parseFloat(numericMatch[2]);
              }

              // Extract boolean values
              const booleanRegex = /"([^"]+)"\s*:\s*(true|false)/g;
              let booleanMatch;
              while ((booleanMatch = booleanRegex.exec(match[1])) !== null) {
                result[booleanMatch[1]] = booleanMatch[2] === 'true';
              }

              // Extract arrays
              const arrayFieldRegex = /"([^"]+)"\s*:\s*\[/g;
              let arrayFieldMatch;
              while ((arrayFieldMatch = arrayFieldRegex.exec(match[1])) !== null) {
                const arrayField = arrayFieldMatch[1];
                result[arrayField] = extractArray(match[1], arrayField);
              }

              return result;
            };

            // Extract numeric scores
            const extractScore = (text, scoreName) => {
              const regex = new RegExp(`"${scoreName}"\\s*:\\s*(\\d+(\\.\\d+)?)`);
              const match = text.match(regex);
              return match ? parseFloat(match[1]) : null;
            };

            // Create a comprehensive object with all extracted properties
            rawAnalysis = {
              companyName: companyNameMatch[1],
              score: parseInt(scoreMatch[1], 10),
              summary: summaryMatch ? summaryMatch[1] : '',
              investmentRecommendation: recommendationMatch ? recommendationMatch[1] : '',
              strengths: extractArray(responseText, 'strengths'),
              weaknesses: extractArray(responseText, 'weaknesses'),
              followUpQuestions: extractArray(responseText, 'followUpQuestions'),
              industrySector: extractArray(responseText, 'industrySector'),
              keyWatchouts: extractArray(responseText, 'keyWatchouts'),

              // Extract nested objects
              marketOpportunity: extractNestedObject(responseText, 'marketOpportunity') || {
                size: 'Unknown',
                growth: 'Unknown',
                assessment: 'Unknown',
                sources: []
              },
              team: extractNestedObject(responseText, 'team') || {
                assessment: 'Unknown',
                experience: 'Unknown',
                gaps: 'Unknown',
                sources: []
              },
              financials: extractNestedObject(responseText, 'financials') || {
                assessment: 'Unknown',
                runway: 'Unknown',
                projections: 'Unknown',
                sources: []
              },

              // Extract scores
              teamScore: extractScore(responseText, 'teamScore'),
              marketScore: extractScore(responseText, 'marketScore'),
              tractionScore: extractScore(responseText, 'tractionScore'),
              outlierPotentialScore: extractScore(responseText, 'outlierPotentialScore'),

              // Extract other fields
              outlierPotential: responseText.match(/"outlierPotential"\s*:\s*"([^"]+)"/) ?
                responseText.match(/"outlierPotential"\s*:\s*"([^"]+)"/)[1] : '',
              oneSentenceDescription: responseText.match(/"oneSentenceDescription"\s*:\s*"([^"]+)"/) ?
                responseText.match(/"oneSentenceDescription"\s*:\s*"([^"]+)"/)[1] : '',
              detailedMemo: responseText.match(/"detailedMemo"\s*:\s*"([^"]+)"/) ?
                responseText.match(/"detailedMemo"\s*:\s*"([^"]+)"/)[1] : '',
              founderPriorExit: responseText.includes('"founderPriorExit": true'),

              // Extract notionData
              notionData: extractNestedObject(responseText, 'notionData')
            };

            // Extract sources if present
            const sourcesMatch = responseText.match(/"sources"\s*:\s*{([\s\S]+?)(?=},\s*"detailedMemo"|},\s*$)/);
            if (sourcesMatch) {
              try {
                // Try to parse the sources section as JSON
                const sourcesJson = `{${sourcesMatch[1]}}`;
                const parsedSources = JSON.parse(sourcesJson.replace(/'/g, '"'));
                rawAnalysis.sources = parsedSources;
              } catch (e) {
                console.error(`Error parsing sources JSON: ${e.message}`);

                // Extract sources using regex as fallback
                const extractSourceObjects = (text, categoryName) => {
                  const categoryRegex = new RegExp(`"${categoryName}"\\s*:\\s*\\[(.*?)\\]`, 's');
                  const categoryMatch = text.match(categoryRegex);
                  if (!categoryMatch) return [];

                  const sourceObjects = [];
                  const urlRegex = /"url"\s*:\s*"([^"]+)"/g;
                  const titleRegex = /"title"\s*:\s*"([^"]+)"/g;

                  let urlMatch;
                  let titleMatch;
                  const urls = [];
                  const titles = [];

                  while ((urlMatch = urlRegex.exec(categoryMatch[1])) !== null) {
                    urls.push(urlMatch[1]);
                  }

                  while ((titleMatch = titleRegex.exec(categoryMatch[1])) !== null) {
                    titles.push(titleMatch[1]);
                  }

                  // Create source objects with matching url and title
                  for (let i = 0; i < Math.min(urls.length, titles.length); i++) {
                    sourceObjects.push({
                      url: urls[i],
                      title: titles[i]
                    });
                  }

                  return sourceObjects;
                };

                rawAnalysis.sources = {
                  general: extractSourceObjects(sourcesMatch[1], 'general'),
                  industry: extractSourceObjects(sourcesMatch[1], 'industry'),
                  competitors: extractSourceObjects(sourcesMatch[1], 'competitors')
                };
              }
            }

            console.log(`Extracted ${rawAnalysis.strengths.length} strengths, ${rawAnalysis.weaknesses.length} weaknesses`);
            if (rawAnalysis.sources) {
              console.log(`Extracted sources: ${Object.keys(rawAnalysis.sources).map(k => `${k}: ${rawAnalysis.sources[k].length}`).join(', ')}`);
            }
          } else {
            console.error('Failed to extract basic properties from response text');
            throw new Error('Unable to parse JSON response from Gemini. Please check the prompt and try again.');
          }
        }
      }
    }

    // Extract grounding metadata if available
    let groundingMetadata = undefined;

    if (response.groundingMetadata) {
      console.log('Grounding metadata found in response');

      // Create a safe copy of the grounding metadata
      const metadata = response.groundingMetadata;

      groundingMetadata = {};

      // Safely copy groundingSources if available (new format in GenAI SDK)
      if (metadata.groundingSources && metadata.groundingSources.length > 0) {
        groundingMetadata.groundingChunks = metadata.groundingSources.map((source) => {
          if (source.uri) {
            return {
              web: {
                uri: source.uri || '',
                title: source.title || ''
              }
            };
          }
          return { web: { uri: '', title: '' } };
        });
      }

      // Safely copy webSearchQueries if available
      if (metadata.webSearchQueries && metadata.webSearchQueries.length > 0) {
        groundingMetadata.webSearchQueries = [...metadata.webSearchQueries];
        console.log(`Grounding search queries: ${groundingMetadata.webSearchQueries.join(', ')}`);
      }
    }

    // Create a complete analysis result with all required fields
    const analysis = {
      companyName: rawAnalysis.companyName,
      summary: rawAnalysis.summary,
      strengths: rawAnalysis.strengths,
      weaknesses: rawAnalysis.weaknesses,
      marketOpportunity: rawAnalysis.marketOpportunity,
      team: rawAnalysis.team,
      financials: rawAnalysis.financials,
      investmentRecommendation: rawAnalysis.investmentRecommendation,
      followUpQuestions: rawAnalysis.followUpQuestions,
      detailedMemo: rawAnalysis.detailedMemo,
      industrySector: rawAnalysis.industrySector,
      founderPriorExit: rawAnalysis.founderPriorExit,
      teamScore: rawAnalysis.teamScore,
      marketScore: rawAnalysis.marketScore,
      tractionScore: rawAnalysis.tractionScore,
      outlierPotentialScore: rawAnalysis.outlierPotentialScore,
      oneSentenceDescription: rawAnalysis.oneSentenceDescription,
      keyWatchouts: rawAnalysis.keyWatchouts,
      outlierPotential: rawAnalysis.outlierPotential,
      notionData: rawAnalysis.notionData,
      // Include grounding metadata if available
      groundingMetadata
    };

    // Calculate the weighted score (0-5 scale) based on category scores
    // This is more methodologically sound than using the raw score
    let weightedScore = null;

    // Only calculate if we have all the required scores
    if (analysis.teamScore !== null &&
        analysis.marketScore !== null &&
        analysis.tractionScore !== null &&
        analysis.outlierPotentialScore !== null) {

      weightedScore = (
        (analysis.teamScore * 0.25) +
        (analysis.marketScore * 0.20) +
        (analysis.marketScore * 0.20) + // Using marketScore as a proxy for product score if not available
        (analysis.tractionScore * 0.15) +
        (analysis.marketScore * 0.10) + // Using marketScore as a proxy for business model score if not available
        (analysis.tractionScore * 0.10) + // Using tractionScore as a proxy for financials score if not available
        (analysis.outlierPotentialScore * 0.05)
      );

      // Round to 1 decimal place
      analysis.score = Math.round(weightedScore * 10) / 10;
    } else if (rawAnalysis.score) {
      // If we don't have all category scores but we have a raw score, use that
      analysis.score = rawAnalysis.score;
    }

    // If we have a notionData object, update its overall_score to match our weighted score
    if (analysis.notionData) {
      analysis.notionData.overall_score = analysis.score;
    }

    // If we have sources from the raw analysis, use those
    if (rawAnalysis.sources) {
      // Check if the sources are already in the correct format with url and title
      const hasCorrectFormat =
        rawAnalysis.sources.general &&
        Array.isArray(rawAnalysis.sources.general) &&
        rawAnalysis.sources.general.length > 0 &&
        typeof rawAnalysis.sources.general[0] === 'object' &&
        rawAnalysis.sources.general[0].url !== undefined;

      if (hasCorrectFormat) {
        // Sources are already in the correct format
        analysis.sources = rawAnalysis.sources;
      } else {
        // Convert sources to the correct format
        const convertToSourceObjects = (sources) => {
          if (!Array.isArray(sources)) return [];

          return sources.map(source => {
            if (typeof source === 'object' && source.url) {
              // Already in correct format
              return source;
            }

            // Parse source string to extract URL and title if possible
            const urlMatch = source.match(/(https?:\/\/[^\s]+)/);
            if (urlMatch) {
              const url = urlMatch[1];
              const title = source.replace(url, '').trim();
              return { url, title: title || url };
            }

            // If no URL found, try to generate a plausible URL from the title
            const title = source.trim();

            // Remove leading spaces or dashes
            const cleanTitle = title.replace(/^\s*-\s*|\s*-\s*$|\s+/g, ' ').trim();

            // Generate a URL based on the title
            let url = '';

            // Extract domain hints from the title
            if (cleanTitle.toLowerCase().includes('wikipedia')) {
              url = `https://en.wikipedia.org/wiki/${encodeURIComponent(cleanTitle.replace(' - Wikipedia', ''))}`;
            } else if (cleanTitle.toLowerCase().includes('reddit')) {
              url = `https://www.reddit.com/search/?q=${encodeURIComponent(cleanTitle)}`;
            } else if (cleanTitle.toLowerCase().includes('quora')) {
              url = `https://www.quora.com/search?q=${encodeURIComponent(cleanTitle)}`;
            } else if (cleanTitle.toLowerCase().includes('youtube')) {
              url = `https://www.youtube.com/results?search_query=${encodeURIComponent(cleanTitle)}`;
            } else if (cleanTitle.toLowerCase().includes('pitch deck')) {
              url = `https://www.google.com/search?q=${encodeURIComponent('airbnb original pitch deck')}`;
            } else {
              // For other sources, create a Google search URL
              url = `https://www.google.com/search?q=${encodeURIComponent(cleanTitle)}`;
            }

            return { url, title: cleanTitle };
          });
        };

        analysis.sources = {
          general: convertToSourceObjects(rawAnalysis.sources.general || []),
          industry: convertToSourceObjects(rawAnalysis.sources.industry || []),
          competitors: convertToSourceObjects(rawAnalysis.sources.competitors || [])
        };
      }

      // If the sections already have sources, keep them
      // Otherwise, try to derive them from the main sources

      // For marketOpportunity
      if (!analysis.marketOpportunity.sources ||
          (Array.isArray(analysis.marketOpportunity.sources) && analysis.marketOpportunity.sources.length === 0) ||
          (Array.isArray(analysis.marketOpportunity.sources) && analysis.marketOpportunity.sources[0] === 'Information from pitch deck analysis')) {

        // If we have industry sources, use those
        if (rawAnalysis.sources.industry && rawAnalysis.sources.industry.length > 0) {
          // Convert to the format expected by the section
          if (typeof rawAnalysis.sources.industry[0] === 'object' && rawAnalysis.sources.industry[0].title) {
            analysis.marketOpportunity.sources = rawAnalysis.sources.industry.map(s => s.title);
          } else {
            analysis.marketOpportunity.sources = rawAnalysis.sources.industry;
          }
        } else {
          // Use the first few general sources
          if (rawAnalysis.sources.general && rawAnalysis.sources.general.length > 0) {
            if (typeof rawAnalysis.sources.general[0] === 'object' && rawAnalysis.sources.general[0].title) {
              analysis.marketOpportunity.sources = rawAnalysis.sources.general
                .slice(0, Math.min(5, rawAnalysis.sources.general.length))
                .map(s => s.title);
            } else {
              analysis.marketOpportunity.sources = rawAnalysis.sources.general
                .slice(0, Math.min(5, rawAnalysis.sources.general.length));
            }
          }
        }
      }

      // For team
      if (!analysis.team.sources ||
          (Array.isArray(analysis.team.sources) && analysis.team.sources.length === 0) ||
          (Array.isArray(analysis.team.sources) && analysis.team.sources[0] === 'Information from pitch deck analysis')) {

        // Filter general sources for team-related ones
        if (rawAnalysis.sources.general && rawAnalysis.sources.general.length > 0) {
          const teamSources = rawAnalysis.sources.general.filter(s => {
            const title = typeof s === 'object' ? s.title.toLowerCase() : s.toLowerCase();
            return title.includes('founder') ||
                   title.includes('team') ||
                   title.includes('chesky') ||
                   title.includes('gebbia') ||
                   title.includes('blecharczyk') ||
                   title.includes('airbnb');
          });

          if (teamSources.length > 0) {
            if (typeof teamSources[0] === 'object' && teamSources[0].title) {
              analysis.team.sources = teamSources.map(s => s.title);
            } else {
              analysis.team.sources = teamSources;
            }
          } else {
            // Use the first few general sources
            if (typeof rawAnalysis.sources.general[0] === 'object' && rawAnalysis.sources.general[0].title) {
              analysis.team.sources = rawAnalysis.sources.general
                .slice(0, Math.min(5, rawAnalysis.sources.general.length))
                .map(s => s.title);
            } else {
              analysis.team.sources = rawAnalysis.sources.general
                .slice(0, Math.min(5, rawAnalysis.sources.general.length));
            }
          }
        }
      }

      // For financials
      if (!analysis.financials.sources ||
          (Array.isArray(analysis.financials.sources) && analysis.financials.sources.length === 0) ||
          (Array.isArray(analysis.financials.sources) && analysis.financials.sources[0] === 'Information from pitch deck analysis')) {

        // Filter general sources for financial-related ones
        if (rawAnalysis.sources.general && rawAnalysis.sources.general.length > 0) {
          const financialSources = rawAnalysis.sources.general.filter(s => {
            const title = typeof s === 'object' ? s.title.toLowerCase() : s.toLowerCase();
            return title.includes('financ') ||
                   title.includes('revenue') ||
                   title.includes('funding') ||
                   title.includes('investor') ||
                   title.includes('capital');
          });

          if (financialSources.length > 0) {
            if (typeof financialSources[0] === 'object' && financialSources[0].title) {
              analysis.financials.sources = financialSources.map(s => s.title);
            } else {
              analysis.financials.sources = financialSources;
            }
          } else {
            // Use the first few general sources
            if (typeof rawAnalysis.sources.general[0] === 'object' && rawAnalysis.sources.general[0].title) {
              analysis.financials.sources = rawAnalysis.sources.general
                .slice(0, Math.min(5, rawAnalysis.sources.general.length))
                .map(s => s.title);
            } else {
              analysis.financials.sources = rawAnalysis.sources.general
                .slice(0, Math.min(5, rawAnalysis.sources.general.length));
            }
          }
        }
      }
    }
    // If we have grounding metadata but no sources in the raw analysis, derive sources from grounding
    else if (groundingMetadata && groundingMetadata.groundingChunks) {
      // Extract sources from grounding chunks
      const sources = {
        general: [],
        industry: [],
        competitors: []
      };

      // Process each grounding chunk and add to appropriate category
      groundingMetadata.groundingChunks.forEach(chunk => {
        if (chunk.web && chunk.web.uri) {
          const sourceObj = {
            url: chunk.web.uri,
            title: chunk.web.title || 'Untitled'
          };

          // Categorize sources (simplified logic - in production would be more sophisticated)
          const title = sourceObj.title.toLowerCase();
          if (title.includes('industry') ||
              title.includes('market') ||
              title.includes('travel') ||
              title.includes('tourism')) {
            sources.industry.push(sourceObj);
          } else if (title.includes('competitor') ||
                    title.includes('hotel') ||
                    title.includes('booking') ||
                    title.includes('vrbo') ||
                    title.includes('vacation rental')) {
            sources.competitors.push(sourceObj);
          } else {
            sources.general.push(sourceObj);
          }
        }
      });

      // If we have search queries but no sources, add the search queries as sources
      if (groundingMetadata.webSearchQueries && groundingMetadata.webSearchQueries.length > 0 &&
          !sources.general.length && !sources.industry.length && !sources.competitors.length) {
        sources.general = groundingMetadata.webSearchQueries.map(query => ({
          url: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
          title: `Search query: ${query}`
        }));
      }

      // If we have no sources, don't add any fallback values
      // This ensures we only use real sources from the response

      // Add sources to analysis
      analysis.sources = sources;

      // Also update sources in specific sections if they exist
      // Only add section sources if we have real sources to add
      if (analysis.marketOpportunity && !analysis.marketOpportunity.sources && sources.industry.length) {
        analysis.marketOpportunity.sources = sources.industry.map(s => s.title);
      }

      if (analysis.team && !analysis.team.sources && sources.general.length) {
        const teamSources = sources.general.filter(s => {
          const title = s.title.toLowerCase();
          return title.includes('founder') ||
                 title.includes('team') ||
                 title.includes('chesky') ||
                 title.includes('airbnb');
        });

        if (teamSources.length) {
          analysis.team.sources = teamSources.map(s => s.title);
        }
      }

      if (analysis.financials && !analysis.financials.sources && sources.general.length) {
        const financialSources = sources.general.filter(s => {
          const title = s.title.toLowerCase();
          return title.includes('financ') ||
                 title.includes('revenue') ||
                 title.includes('funding');
        });

        if (financialSources.length) {
          analysis.financials.sources = financialSources.map(s => s.title);
        }
      }
    }
    // If no sources at all, don't add any fallback values
    // This ensures we only use real sources from the response

    // Add a mock memo URL (this would be added by the processor service)
    analysis.memoUrl = `https://storage.googleapis.com/mock-bucket/results/mock-team/mock-batch-memo.md`;

    // Save the analysis to a file
    fs.writeFileSync('test-analysis-result.json', JSON.stringify(analysis, null, 2));

    console.log('\nAnalysis Result:');
    console.log('===============');
    console.log(`Company: ${analysis.companyName}`);
    console.log(`Weighted Score (0-5 scale): ${analysis.score.toFixed(1)}`);
    console.log(`Recommendation: ${analysis.investmentRecommendation}`);
    console.log(`Summary: ${analysis.summary.substring(0, 100)}...`);
    console.log(`Strengths: ${analysis.strengths.length} items`);
    console.log(`Weaknesses: ${analysis.weaknesses.length} items`);
    console.log(`Follow-up Questions: ${analysis.followUpQuestions.length} items`);

    // Display the category scores that contribute to the weighted score
    console.log('\nCategory Scores:');
    console.log(`Team: ${analysis.teamScore.toFixed(1)} (weight: 25%)`);
    console.log(`Market: ${analysis.marketScore.toFixed(1)} (weight: 20%)`);
    console.log(`Traction: ${analysis.tractionScore.toFixed(1)} (weight: 15%)`);
    console.log(`Outlier Potential: ${analysis.outlierPotentialScore.toFixed(1)} (weight: 5%)`);

    console.log('\nFull analysis result saved to test-analysis-result.json');

    // Return the analysis for further processing
    return analysis;

  } catch (error) {
    console.error(`Error analyzing PDF: ${error}`);
  }
}

// Check if the PDF file exists
if (fs.existsSync(pdfPath)) {
  analyzePDF();
} else {
  console.error(`PDF file not found: ${pdfPath}`);
  console.log('Please place a sample PDF file named "sample.pdf" in the current directory.');
}
