import { GoogleGenAI } from '@google/genai';

// Initialize Vertex with your Cloud project and location
const ai = new GoogleGenAI({
  vertexai: true,
  project: 'valurize-app',
  location: 'us-central1'
});
const model = 'gemini-2.5-pro-preview-05-06';

const siText1 = {text: `You are a helpful AI assistant that provides accurate and up-to-date information.
      When answering questions, please:
      1. Use web search to find the most current information
      2. Cite your sources with links
      3. Be concise but thorough
      4. Focus on factual information`};

// Add Google Search tool for grounding
const tools = [
  {
    googleSearch: {}
  }
];

// Set up generation config
const generationConfig = {
  maxOutputTokens: 8192,
  temperature: 0.6,
  topP: 0.66,
  seed: 0,
  responseModalities: ["TEXT"],
  safetySettings: [
    {
      category: 'HARM_CATEGORY_HATE_SPEECH',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      threshold: 'OFF',
    },
    {
      category: 'HARM_CATEGORY_HARASSMENT',
      threshold: 'OFF',
    }
  ],
  tools: tools,
  systemInstruction: {
    parts: [siText1]
  },
};

async function generateContent() {
  const req = {
    model: model,
    contents: [
      {
        role: "user",
        parts: [
          {
            text: `What are the latest updates on relations between India and Pakistan as of May 9th, 2025?
            Please provide specific recent events, diplomatic exchanges, or tensions.
            Include dates and sources where possible.`
          }
        ]
      }
    ],
    config: generationConfig,
  };

  const streamingResp = await ai.models.generateContentStream(req);

  for await (const chunk of streamingResp) {
    if (chunk.text) {
      process.stdout.write(chunk.text);
    } else {
      process.stdout.write(JSON.stringify(chunk) + '\n');
    }
  }
}

generateContent();
