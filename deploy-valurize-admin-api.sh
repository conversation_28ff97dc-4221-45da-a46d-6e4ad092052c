#!/bin/bash

# Deploy script for Valurize app (admin-api only) with CORS fix
# This script explicitly targets the valurize-app project

set -e

# Configuration - explicitly set to valurize-app
PROJECT_ID="valurize-app"
REGION="us-central1"

echo "Deploying Admin API with CORS fix to project: ${PROJECT_ID}, region: ${REGION}"

# First, update the CORS configuration in the index.ts file
echo "Updating CORS configuration..."
cd services/admin-api/src
cat > cors-config.ts << 'EOL'
import cors from 'cors';

// Configure CORS options
export const corsOptions = {
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};

// Create CORS middleware
export const corsMiddleware = cors(corsOptions);
EOL

# Update the index.ts file to use the new CORS configuration
sed -i '' 's/app.use(cors());/import { corsMiddleware } from '\''\.\/cors-config'\''\;\napp.use(corsMiddleware);/' index.ts

cd ..

# Build TypeScript files
echo "Building TypeScript files..."
npm install
npm run build

# Deploy to Cloud Run - explicitly specify project
gcloud builds submit --tag gcr.io/${PROJECT_ID}/admin-api --project=${PROJECT_ID}
gcloud run deploy admin-api \
  --image gcr.io/${PROJECT_ID}/admin-api \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --concurrency 10 \
  --min-instances 0 \
  --max-instances 10 \
  --project=${PROJECT_ID} \
  --set-env-vars="GOOGLE_CLOUD_PROJECT=${PROJECT_ID},NODE_ENV=production,LOG_LEVEL=info,JWT_SECRET=${JWT_SECRET:-valurize-secret-key},SLACK_INGEST_URL=https://slack-ingest-${PROJECT_ID}.a.run.app,NOTION_SYNC_URL=https://notion-sync-${PROJECT_ID}.a.run.app,TENANT_API_URL=https://tenant-${PROJECT_ID}.a.run.app,PROCESSOR_URL=https://processor-${PROJECT_ID}.a.run.app,SLACK_NOTIFY_URL=https://slack-notify-${PROJECT_ID}.a.run.app,EMAIL_INGEST_URL=https://email-ingest-${PROJECT_ID}.a.run.app,FRONTEND_URL=${FRONTEND_URL:-https://admin.valurize.co},NOTION_CLIENT_ID=${NOTION_CLIENT_ID},SLACK_CLIENT_ID=${SLACK_CLIENT_ID},SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET},SLACK_SIGNING_SECRET=${SLACK_SIGNING_SECRET},CORS_ORIGIN=*"

echo "Deployment completed successfully!"
