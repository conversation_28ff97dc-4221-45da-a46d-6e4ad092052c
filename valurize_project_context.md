# Valurize Project Context

## Project Overview
Valurize is a multi-tenant, scalable pitch deck analysis platform for VCs on GCP. It allows deck submission via Slack/email/API, tenant identification, usage verification, AI analysis across multiple dimensions, and result delivery through multiple channels with tiered service levels.

## Project Structure
The project has a hybrid structure with:
1. A root-level `src` directory containing shared code
2. A `services` directory with individual microservices

### Root-Level Components
- `src/services/geminiService.ts`: Contains the implementation for Gemini 2.5 Pro integration
- `src/middleware`: Shared middleware
- `src/routes`: Shared route handlers
- `src/utils`: Shared utility functions
- `infra`: Infrastructure as Code (likely Terraform)
- `scripts`: Deployment and utility scripts

### Microservices
- `services/slack-ingest`: Handles Slack commands and file uploads
- `services/email-ingest`: Handles email-based PDF submissions
- `services/processor`: Processes PDFs using AI
- `services/slack-notify`: Formats and sends results to Slack
- `services/notion-sync`: Syncs results to Notion databases
- `services/tenant`: Manages tenant information and usage
- `services/admin-api`: Admin operations and overrides
- `services/token-rotator`: Refreshes OAuth tokens
- `services/batch-manager`: Manages batch processing of PDFs

## Implementation Status

### Gemini Integration
- **Status**: Partially implemented
- **Details**:
  - The `geminiService.ts` file is implemented in the root `src/services` directory
  - It uses Gemini 2.5 Pro Preview on Vertex AI
  - The processor service references this file but doesn't have the necessary dependency
  - The `@google-cloud/aiplatform` dependency is missing from all package.json files
  - Vertex AI environment variables are configured in the processor service

### Discrepancies and Issues
1. **Missing Dependencies**: The `@google-cloud/aiplatform` dependency is not in any package.json
2. **Import Path Issue**: The processor service imports from `./services/geminiService` but the file doesn't exist in that location
3. **OpenAI Dependency**: The processor service still has the OpenAI dependency which should be replaced

## Required Actions
1. Add the `@google-cloud/aiplatform` dependency to the processor service
2. Either:
   - Copy the geminiService.ts file to the processor service's services directory, or
   - Update the import path to reference the root-level implementation
3. Remove the OpenAI dependency from the processor service
4. Ensure the processor service has the correct Vertex AI environment variables

## Data Flow
1. Files are uploaded via Slack or email
2. Files are stored in GCS with tenant-specific paths
3. Messages are published to Pub/Sub topics
4. The processor service processes PDFs using Gemini
5. Results are stored in GCS and Firestore
6. Notifications are sent via Slack
7. Results are synced to Notion if configured

## Multi-Tenant Architecture
- Data is isolated by teamId in Firestore collections
- GCS paths include teamId for isolation
- Tokens are stored in Secret Manager with tenant-specific names
- Usage is tracked per tenant with tiered limits
