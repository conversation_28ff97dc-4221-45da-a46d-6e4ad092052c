# Valurize

A multi-tenant, infinitely scalable system for VCs to analyze pitch decks in Slack and email.

## Architecture

Valurize is built on Google Cloud Platform with the following components:

- **Compute & Ingress**: Cloud Run (2nd gen) for all HTTP-triggered services
- **Storage & State**: GCS buckets and Firestore collections
- **Queueing & Retries**: Pub/Sub topics with Dead-Letter topics
- **Monitoring & Security**: Cloud Monitoring, Cloud Logging, IAM, and Secret Manager

## Services

- **Tenant API**: Manages tenant configuration and billing
- **Slack Integration**: Handles Slack commands and file uploads
- **Email Integration**: Processes email attachments via Mailgun
- **Notion Integration**: Syncs analysis results to Notion
- **PDF Processing**: Analyzes pitch decks using Gemini 2.5 Pro on Vertex AI
- **Slack Notify**: Sends analysis results back to Slack
- **Admin API**: Provides administrative functions

## Key Features

- **Sequential Processing**: PDF processor handles one request at a time per container
- **Batch Processing**: PDFs are grouped by user and processed in batches
- **Gemini 2.5 Pro Integration**: Uses Google's advanced AI model for pitch deck analysis
- **Web Grounding**: Analysis is grounded with Google Search results
- **Multi-Channel Delivery**: Results delivered via Slack, Email, and Notion
- **Scalability**: Supports up to 100 simultaneous users with 100 PDFs each

## Development

### Prerequisites

- Node.js 18+
- Terraform 1.0+
- Google Cloud SDK

### Setup

1. Clone the repository
2. Run `npm install` to install dependencies
3. Set up GCP credentials:
   ```bash
   gcloud auth login
   gcloud auth application-default login
   ```
4. Create a GCP project:
   ```bash
   gcloud projects create valurize-app --name="Valurize"
   gcloud config set project valurize-app
   ```
5. Enable billing for the project
6. Enable required APIs:
   ```bash
   gcloud services enable run.googleapis.com cloudscheduler.googleapis.com pubsub.googleapis.com firestore.googleapis.com storage.googleapis.com secretmanager.googleapis.com monitoring.googleapis.com logging.googleapis.com aiplatform.googleapis.com
   ```
7. Set up environment variables in a `.env` file:
   ```
   GOOGLE_CLOUD_PROJECT=valurize-app
   FIREBASE_CLIENT_EMAIL=<EMAIL>
   FIREBASE_PRIVATE_KEY="your-private-key"
   SLACK_CLIENT_ID=your-slack-client-id
   SLACK_CLIENT_SECRET=your-slack-client-secret
   SLACK_SIGNING_SECRET=your-slack-signing-secret
   MAILGUN_API_KEY=your-mailgun-api-key
   MAILGUN_DOMAIN=decks.valurize.co
   ```

## Deployment

### Manual Deployment

Run the deployment script:
```bash
./scripts/deploy.sh
```

This will:
1. Apply the Terraform configuration
2. Build and deploy all 7 microservices:
   - Tenant API
   - Slack Integration
   - Email Integration
   - PDF Processor
   - Slack Notify
   - Notion Sync
   - Admin API

### CI/CD Deployment

Deployment is also handled via GitHub Actions. Push to the main branch to trigger a deployment.

## Usage

### Tenant Management

1. Create a tenant:
   ```bash
   curl -X POST http://tenant-api-xxxxx-uc.a.run.app/tenant/test-team -H "Content-Type: application/json" -d '{"tier": "free", "prompt": "Analyze this pitch deck", "thresholds": {"notionSync": 70}}'
   ```

### Tenant Onboarding Flow

1. Create a new tenant through the admin dashboard
2. Generate a Slack installation link for the tenant
3. Share the link with the tenant to install the Slack app
4. Generate a Notion connection link for the tenant
5. Share the link with the tenant to connect their Notion workspace

### Slack Integration

1. Connect your Slack workspace using the Slack App configuration
2. Use the `/uploaddeck` command to upload pitch decks
3. View top decks with the `/topdecks` command
4. Connect to Notion with the `/connect-notion` command

### Email Integration

1. Send emails with PDF attachments to `<EMAIL>`
2. Receive analysis results via email reply

### Notion Integration

1. Connect your Notion workspace using the OAuth flow
2. Select a database for syncing analysis results
3. Configure field mappings and score threshold

### Admin Dashboard

1. Access the admin dashboard at `http://admin-api-xxxxx-uc.a.run.app`
2. Login with default credentials: `<EMAIL>` / `admin123`
3. Manage tenants, users, and view analytics
4. Create new tenants and manage their onboarding process
5. Generate Slack installation and Notion connection links for tenants
6. Track tenant onboarding status and usage

## Service Configuration

### PDF Processor
- **Concurrency**: 1 (each container handles exactly one user's entire batch)
- **Max Instances**: 100 (up to 100 users at once)
- **Memory**: 1 GB (handles base64 + Gemini buffer)
- **Timeout**: 900 s (15 min) to cover long 100-PDF jobs

### Other Services
- **Concurrency**: 10
- **Memory**: 512 MB
- **Timeout**: Default (60s)
