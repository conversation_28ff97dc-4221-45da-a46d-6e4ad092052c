import { analyzeDocument, AnalysisResult } from './src/services/geminiService';
import * as fs from 'fs';
import * as path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Mock implementation of the Gemini service for testing
 * This is used when USE_MOCK=true or when credentials are missing
 */
async function mockAnalyzeDocument(fileBuffer: Buffer, prompt: string): Promise<AnalysisResult> {
  console.log(`Mock analyzing document with prompt: ${prompt.substring(0, 50)}...`);
  console.log(`PDF size: ${fileBuffer.length} bytes`);
  
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Return mock analysis result
  return {
    companyName: "TechStartup Inc.",
    score: 78,
    summary: "TechStartup Inc. is an AI-powered platform for business optimization with strong team credentials but some financial projection concerns.",
    strengths: [
      "Experienced founding team with relevant industry background",
      "Large and growing target market ($45B)",
      "Strong product features addressing clear market needs",
      "Early traction with Fortune 500 customers"
    ],
    weaknesses: [
      "Ambitious financial projections may be difficult to achieve",
      "Limited information on competitive differentiation",
      "High burn rate in early years",
      "Unclear customer acquisition strategy"
    ],
    marketOpportunity: {
      size: "The global AI market is substantial at $190B by 2025",
      growth: "Strong growth rate at 37% CAGR",
      assessment: "Excellent market opportunity with significant growth potential"
    },
    team: {
      assessment: "Strong founding team with relevant experience",
      experience: "30+ years combined experience with backgrounds at top tech companies",
      gaps: "Potential gap in sales leadership"
    },
    financials: {
      assessment: "Ambitious projections with high initial burn rate",
      runway: "The $5M raise provides approximately 18 months of runway",
      projections: "Year 3 revenue projections of $8M seem optimistic"
    },
    investmentRecommendation: "Proceed with caution. The strong team and market opportunity make this an interesting investment, but financial projections should be scrutinized further.",
    followUpQuestions: [
      "What is your customer acquisition strategy and cost?",
      "How do you differentiate from competitors?",
      "Can you provide more details on your current pilot customers?",
      "What are the key milestones for the next 18 months?"
    ]
  };
}

/**
 * Test the Gemini service
 * This will use the real service if USE_MOCK=false and credentials are available
 * Otherwise, it will fall back to the mock implementation
 */
async function testGeminiService() {
  try {
    console.log('Starting Gemini service test...');
    
    // Path to a sample PDF file
    const pdfPath = process.env.TEST_PDF_PATH || 'sample.pdf';
    
    if (!fs.existsSync(pdfPath)) {
      console.error(`Error: Test PDF file not found at ${pdfPath}`);
      console.log('Please set TEST_PDF_PATH environment variable to a valid PDF file path');
      return;
    }
    
    // Load the PDF file
    console.log(`Loading PDF from ${pdfPath}...`);
    const fileBuffer = fs.readFileSync(pdfPath);
    
    // Custom prompt for testing
    const testPrompt = `
    Analyze this pitch deck with the following criteria:
    
    1. Team (25%): Evaluate the founding team's experience, domain expertise, and track record.
    2. Market (20%): Assess market size, growth potential, and competitive landscape.
    3. Product (20%): Evaluate product-market fit, innovation, and technical feasibility.
    4. Traction (15%): Analyze current customers, revenue, and growth metrics.
    5. Business Model (10%): Evaluate revenue model, unit economics, and scalability.
    6. Financials (10%): Assess financial projections, funding needs, and use of funds.
    
    Be critical but fair in your assessment.
    `;
    
    // Determine whether to use mock or real service
    const useMock = process.env.USE_MOCK === 'true' || 
                    !process.env.GOOGLE_APPLICATION_CREDENTIALS || 
                    !process.env.VERTEX_AI_PROJECT;
    
    console.log(`Using ${useMock ? 'MOCK' : 'REAL'} Gemini service...`);
    console.log('Sending PDF for analysis...');
    
    const startTime = Date.now();
    
    // Call the appropriate service
    let result: AnalysisResult;
    if (useMock) {
      result = await mockAnalyzeDocument(fileBuffer, testPrompt);
    } else {
      result = await analyzeDocument(fileBuffer, testPrompt);
    }
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log(`Analysis completed in ${processingTime.toFixed(2)} seconds`);
    console.log('\nAnalysis Results:');
    console.log('=================');
    console.log(`Company: ${result.companyName}`);
    console.log(`Score: ${result.score}/100`);
    console.log(`Summary: ${result.summary}`);
    
    console.log('\nStrengths:');
    result.strengths.forEach((strength, index) => {
      console.log(`${index + 1}. ${strength}`);
    });
    
    console.log('\nWeaknesses:');
    result.weaknesses.forEach((weakness, index) => {
      console.log(`${index + 1}. ${weakness}`);
    });
    
    console.log('\nMarket Opportunity:');
    console.log(`Size: ${result.marketOpportunity.size}`);
    console.log(`Growth: ${result.marketOpportunity.growth}`);
    console.log(`Assessment: ${result.marketOpportunity.assessment}`);
    
    console.log('\nTeam:');
    console.log(`Assessment: ${result.team.assessment}`);
    console.log(`Experience: ${result.team.experience}`);
    console.log(`Gaps: ${result.team.gaps}`);
    
    console.log('\nFinancials:');
    console.log(`Assessment: ${result.financials.assessment}`);
    console.log(`Runway: ${result.financials.runway}`);
    console.log(`Projections: ${result.financials.projections}`);
    
    console.log('\nInvestment Recommendation:');
    console.log(result.investmentRecommendation);
    
    console.log('\nFollow-up Questions:');
    result.followUpQuestions.forEach((question, index) => {
      console.log(`${index + 1}. ${question}`);
    });
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing Gemini service:', error);
  }
}

// Run the test
testGeminiService();
