import * as fs from 'fs';
import dotenv from 'dotenv';
import { VertexAI } from '@google-cloud/vertexai';
// Import the base AnalysisResult interface
import { AnalysisResult as BaseAnalysisResult } from './src/services/geminiService';

// Enhanced version of the AnalysisResult interface with sources
interface EnhancedAnalysisResult extends BaseAnalysisResult {
  marketOpportunity: {
    size: string;
    growth: string;
    assessment: string;
    sources?: string[];
  };
  team: {
    assessment: string;
    experience: string;
    gaps: string;
    sources?: string[];
  };
  financials: {
    assessment: string;
    runway: string;
    projections: string;
    sources?: string[];
  };
  outlierPotential: string;
  sources?: {
    general?: string[];
    industry?: string[];
    competitors?: string[];
  };
  notionData?: {
    startup_name: string;
    one_sentence_description: string;
    analysis_date: string;
    overall_score: number;
    recommendation: string;
    industry_sector: string[];
    founder_prior_exit: boolean;
    team_score: number;
    market_score: number;
    traction_score: number;
    outlier_potential_score: number;
    key_watchouts: string;
  };
}
import { logger } from './src/utils/logger';

// Load environment variables
dotenv.config();

/**
 * Enhanced version of the Gemini service with thinking and Google Search grounding
 * This is a test implementation that doesn't modify the actual service
 */
async function enhancedAnalyzeDocument(
  fileBuffer: Buffer
): Promise<EnhancedAnalysisResult> {
  try {
    // Get project and region from environment variables
    const project = process.env.VERTEX_AI_PROJECT || process.env.GOOGLE_CLOUD_PROJECT;
    const location = process.env.VERTEX_AI_REGION || 'us-central1';

    if (!project) {
      throw new Error('No project ID found in environment variables');
    }

    console.log(`Using project: ${project}, location: ${location}`);

    // Initialize the Vertex AI client
    const vertexAI = new VertexAI({
      project: project,
      location: location,
    });

    // Get the Gemini model
    const generativeModel = vertexAI.getGenerativeModel({
      model: 'gemini-2.5-pro-preview-03-25',
      generationConfig: {
        temperature: 0.2,
        topP: 0.95,
        maxOutputTokens: 8192,
      }
    });

    console.log('Using Gemini 2.5 Pro Preview model with default thinking enabled');

    // Convert the PDF buffer to base64
    const base64PdfString = fileBuffer.toString('base64');

    // Define a single comprehensive prompt with no redundancy
    const analysisPrompt = `
You are Valurize, an AI pitch deck analysis system embodying the critical thinking and analytical rigor of a seasoned Venture Capital partner. Your objective is to conduct a thorough and insightful evaluation of the provided pitch deck, assign scores based on your findings, calculate a weighted total score, and determine a recommendation.

**Core Analysis Task:**
Critically analyze the entire pitch deck based on the criteria below. Leverage your internal reasoning capabilities to assess the realism of statements, gather context, and analyze the information present in the deck.

IMPORTANT:
- Cross-check all information in the pitch deck with publicly available sources
- Research team members' LinkedIn profiles, news archives, Crunchbase, and online presence
- Verify market size claims (TAM, SAM, SOM) with latest industry reports and credible sources
- Check for any recent news about the company or founders
- Use your thinking capabilities to thoroughly analyze this pitch deck
- Take your time to reason through each aspect of the business before providing your final assessment

**Evaluation Criteria & Scoring (Assign 1-5 for each):**
(1: Significant Weakness/Red Flag, 2: Weakness, 3: Average/Adequate, 4: Strength, 5: Exceptional Strength)

1. **Team (Weight: 25%):**
   - Evaluate the founding team's experience, domain expertise, and track record
   - Verify backgrounds using LinkedIn, news archives, Crunchbase
   - Assess relevant experience, domain expertise, completeness
   - Determine if any founder has a verified prior successful exit

2. **Market (Weight: 20%):**
   - Assess market size, growth potential, and competitive landscape
   - Verify claimed TAM, SAM, and SOM against independent market research
   - Evaluate market growth trends and dynamics
   - Identify the primary industry/sector

3. **Product (Weight: 20%):**
   - Evaluate product-market fit, innovation, and technical feasibility
   - Research similar products/services in the market
   - Assess technological feasibility and implementation challenges
   - Evaluate intellectual property position and defensibility

4. **Traction (Weight: 15%):**
   - Analyze current customers, revenue, and growth metrics
   - Substantiate claimed milestones using external validation
   - Differentiate meaningful progress from vanity metrics
   - Assess stage-appropriateness

5. **Business Model (Weight: 10%):**
   - Evaluate revenue model, unit economics, and scalability
   - Compare with industry benchmarks
   - Assess pricing strategy against competitors
   - Research customer acquisition costs and lifetime value benchmarks

6. **Financials (Weight: 10%):**
   - Assess financial projections, funding needs, and use of funds
   - Evaluate the realism of forecasts and underlying assumptions
   - Compare key metrics against industry benchmarks
   - Assess capital efficiency and runway implications

7. **Outlier Potential (Weight: 5%):**
   - Look for signals of a potential high-risk/high-reward outlier
   - Assess if targeting a non-obvious problem/market
   - Evaluate for highly unconventional approach
   - Check for evidence of intense early user love in a niche
   - Look for potential for market creation
   - Assess for exceptional founder vision/obsession that deviates from norms

**Weighted Score Calculation:**
Calculate the Total Score by summing the weighted scores (Score * Weight) for all criteria. The maximum possible score is 5.0.

**Recommendation Logic:**
Based on the calculated Total Score:
- High Priority (Invest): Total Score >= 4.0
- Maybe (Further Review): Total Score >= 3.0 and < 4.0
- Pass: Total Score < 3.0

**Output Requirements:**
Provide your response in the following JSON format:
{
  "companyName": "Name of the company",
  "score": <Calculated Total Weighted Score, scaled to 0-100, e.g., (Total Score / 5.0) * 100>,
  "summary": "Brief executive summary of the company and opportunity",
  "strengths": ["Strength 1", "Strength 2", ...],
  "weaknesses": ["Weakness 1", "Weakness 2", ...],
  "marketOpportunity": {
    "size": "Market size assessment",
    "growth": "Market growth assessment",
    "assessment": "Overall market opportunity assessment",
    "sources": ["Source 1", "Source 2", ...] // Add sources for market information
  },
  "team": {
    "assessment": "Overall team assessment",
    "experience": "Team experience assessment",
    "gaps": "Identified team gaps",
    "sources": ["Source 1", "Source 2", ...] // Add sources for team information
  },
  "financials": {
    "assessment": "Overall financial assessment",
    "runway": "Runway assessment",
    "projections": "Projection realism assessment",
    "sources": ["Source 1", "Source 2", ...] // Add sources for financial information
  },
  "outlierPotential": "Assessment of potential for high-risk/high-reward outlier success",
  "investmentRecommendation": "Clear recommendation based on the logic above (High Priority, Maybe, Pass)",
  "followUpQuestions": ["Question 1", "Question 2", ...],
  "sources": {
    "general": ["Source 1", "Source 2", ...], // General sources used for the analysis
    "industry": ["Source 1", "Source 2", ...], // Industry-specific sources
    "competitors": ["Source 1", "Source 2", ...] // Sources for competitor information
  },
  "notionData": {
    "startup_name": "Name of the company",
    "one_sentence_description": "A single sentence description of the company",
    "analysis_date": "Current date",
    "overall_score": <Score out of 5.0, e.g., Total Score / 20>,
    "recommendation": "High Priority, Maybe, or Pass",
    "industry_sector": ["Primary Industry", "Secondary Industry"],
    "founder_prior_exit": true/false,
    "team_score": <Score out of 5.0>,
    "market_score": <Score out of 5.0>,
    "traction_score": <Score out of 5.0>,
    "outlier_potential_score": <Score out of 5.0>,
    "key_watchouts": "• Watchout 1\\n• Watchout 2\\n• Watchout 3"
  }
}
`;

    // Make the request to Gemini
    const request = {
      contents: [
        {
          role: 'user',
          parts: [
            { text: analysisPrompt },
            {
              inlineData: {
                mimeType: 'application/pdf',
                data: base64PdfString
              }
            }
          ]
        }
      ]
    };

    console.log('Generating content with thinking enabled...');
    console.log('Note: Thinking is enabled by default in Gemini 2.5 models');

    // Generate content (thinking is enabled by default in Gemini 2.5)
    const response = await generativeModel.generateContent(request);
    const result = response.response;

    // Check if we have a valid response
    if (!result.candidates || result.candidates.length === 0) {
      throw new Error('No candidates returned from Gemini');
    }

    const candidate = result.candidates[0];
    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
      throw new Error('No content parts found in Gemini response');
    }

    // Extract the text from the response
    const responseText = candidate.content.parts[0].text;

    if (!responseText) {
      throw new Error('No text found in Gemini response');
    }

    // Extract the JSON from the response
    const jsonMatch = responseText.match(/({[\s\S]*})/);

    if (!jsonMatch) {
      throw new Error('No JSON found in Gemini response');
    }

    const jsonString = jsonMatch[1];
    const analysis = JSON.parse(jsonString) as EnhancedAnalysisResult;

    return analysis;
  } catch (error) {
    logger.error(`Error analyzing document with enhanced Gemini: ${error}`);

    // Return a default analysis in case of error
    return {
      companyName: 'Error Processing Document',
      score: 0,
      summary: 'An error occurred while processing this document.',
      strengths: ['Could not analyze strengths'],
      weaknesses: ['Could not analyze weaknesses'],
      marketOpportunity: {
        size: 'Unknown',
        growth: 'Unknown',
        assessment: 'Could not assess market opportunity',
        sources: ['Error: Could not retrieve sources']
      },
      team: {
        assessment: 'Could not assess team',
        experience: 'Unknown',
        gaps: 'Unknown',
        sources: ['Error: Could not retrieve sources']
      },
      financials: {
        assessment: 'Could not assess financials',
        runway: 'Unknown',
        projections: 'Unknown',
        sources: ['Error: Could not retrieve sources']
      },
      outlierPotential: 'Could not assess outlier potential',
      investmentRecommendation: 'Cannot provide recommendation due to processing error',
      followUpQuestions: ['What is the company name?', 'What is the business model?'],
      sources: {
        general: ['Error: Could not retrieve sources']
      }
    };
  }
}

/**
 * This function is no longer needed as we'll ask the AI to generate the Notion JSON directly
 */

/**
 * Generates a beautifully formatted Markdown report from the analysis result
 * @param result The analysis result
 * @returns A formatted Markdown string
 */
function generateMarkdownReport(result: EnhancedAnalysisResult): string {
  // Get current date for the report
  const date = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // No need for color or badge variables since we're using the actual recommendation text

  // Format strengths and weaknesses
  const strengthsList = result.strengths.map(s => `- ${s}`).join('\n');
  const weaknessesList = result.weaknesses.map(w => `- ${w}`).join('\n');

  // Format follow-up questions
  const questionsList = result.followUpQuestions.map(q => `- ${q}`).join('\n');

  // Format sources
  const formatSources = (sources?: string[]) => {
    if (!sources || sources.length === 0) return 'None provided';
    return sources.map(s => `- ${s}`).join('\n');
  };

  // Format general sources
  const generalSources = result.sources?.general ?
    result.sources.general.map(s => `- ${s}`).join('\n') : 'None provided';

  const industrySources = result.sources?.industry ?
    result.sources.industry.map(s => `- ${s}`).join('\n') : 'None provided';

  const competitorSources = result.sources?.competitors ?
    result.sources.competitors.map(s => `- ${s}`).join('\n') : 'None provided';

  // Create the markdown content
  return `# Pitch Deck Analysis: ${result.companyName}

*Generated by Valurize AI on ${date}*

---

## Executive Summary

**Score: ${result.score}/100** | **Recommendation: ${result.investmentRecommendation}**

${result.summary}

---

## Key Strengths

${strengthsList}

---

## Key Weaknesses

${weaknessesList}

---

## Detailed Analysis

### Market Opportunity

**Size:** ${result.marketOpportunity.size}

**Growth:** ${result.marketOpportunity.growth}

**Assessment:** ${result.marketOpportunity.assessment}

**Sources:**
${formatSources(result.marketOpportunity.sources)}

### Team

**Assessment:** ${result.team.assessment}

**Experience:** ${result.team.experience}

**Gaps:** ${result.team.gaps}

**Sources:**
${formatSources(result.team.sources)}

### Financials

**Assessment:** ${result.financials.assessment}

**Runway:** ${result.financials.runway}

**Projections:** ${result.financials.projections}

**Sources:**
${formatSources(result.financials.sources)}

### Outlier Potential

${result.outlierPotential}

---

## Follow-up Questions

${questionsList}

---

## Sources

### General Sources
${generalSources}

### Industry Sources
${industrySources}

### Competitor Sources
${competitorSources}

---

<div align="center">
<i>This analysis was generated by <b>Valurize AI</b>, a pitch deck analysis system that leverages Gemini 2.5 Pro with thinking capabilities and Google Search grounding.</i>
</div>
`;
}

/**
 * Test the enhanced Gemini service with thinking and Google Search grounding
 */
async function testEnhancedGeminiService() {
  try {
    console.log('Starting enhanced Gemini service test with thinking and Google Search grounding...');

    // Path to the PDF file in the test folder
    const pdfPath = './test/Airbnb.pdf';

    if (!fs.existsSync(pdfPath)) {
      console.error(`Error: Test PDF file not found at ${pdfPath}`);
      return;
    }

    // Load the PDF file
    console.log(`Loading PDF from ${pdfPath}...`);
    const fileBuffer = fs.readFileSync(pdfPath);
    console.log(`PDF loaded successfully. Size: ${(fileBuffer.length / 1024).toFixed(2)} KB`);

    // No need for a separate prompt anymore since we have a single comprehensive prompt in the function

    console.log('Sending PDF to enhanced Gemini for analysis...');
    console.log('This may take longer due to thinking and Google Search grounding...');
    const startTime = Date.now();

    // Call the enhanced Gemini service
    const result = await enhancedAnalyzeDocument(fileBuffer);

    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;

    console.log(`Analysis completed in ${processingTime.toFixed(2)} seconds`);
    console.log('\nAnalysis Results:');
    console.log('=================');
    console.log(`Company: ${result.companyName}`);
    console.log(`Score: ${result.score}/100`);
    console.log(`Summary: ${result.summary}`);

    console.log('\nStrengths:');
    result.strengths.forEach((strength: string, index: number) => {
      console.log(`${index + 1}. ${strength}`);
    });

    console.log('\nWeaknesses:');
    result.weaknesses.forEach((weakness: string, index: number) => {
      console.log(`${index + 1}. ${weakness}`);
    });

    console.log('\nMarket Opportunity:');
    console.log(`Size: ${result.marketOpportunity.size}`);
    console.log(`Growth: ${result.marketOpportunity.growth}`);
    console.log(`Assessment: ${result.marketOpportunity.assessment}`);
    if (result.marketOpportunity.sources && result.marketOpportunity.sources.length > 0) {
      console.log('Sources:');
      result.marketOpportunity.sources.forEach((source: string, index: number) => {
        console.log(`  ${index + 1}. ${source}`);
      });
    }

    console.log('\nTeam:');
    console.log(`Assessment: ${result.team.assessment}`);
    console.log(`Experience: ${result.team.experience}`);
    console.log(`Gaps: ${result.team.gaps}`);
    if (result.team.sources && result.team.sources.length > 0) {
      console.log('Sources:');
      result.team.sources.forEach((source: string, index: number) => {
        console.log(`  ${index + 1}. ${source}`);
      });
    }

    console.log('\nFinancials:');
    console.log(`Assessment: ${result.financials.assessment}`);
    console.log(`Runway: ${result.financials.runway}`);
    console.log(`Projections: ${result.financials.projections}`);
    if (result.financials.sources && result.financials.sources.length > 0) {
      console.log('Sources:');
      result.financials.sources.forEach((source: string, index: number) => {
        console.log(`  ${index + 1}. ${source}`);
      });
    }

    console.log('\nOutlier Potential:');
    console.log(result.outlierPotential);

    console.log('\nInvestment Recommendation:');
    console.log(result.investmentRecommendation);

    // Display general sources if available
    if (result.sources && result.sources.general && result.sources.general.length > 0) {
      console.log('\nGeneral Sources:');
      result.sources.general.forEach((source: string, index: number) => {
        console.log(`${index + 1}. ${source}`);
      });
    }

    // Display industry sources if available
    if (result.sources && result.sources.industry && result.sources.industry.length > 0) {
      console.log('\nIndustry Sources:');
      result.sources.industry.forEach((source: string, index: number) => {
        console.log(`${index + 1}. ${source}`);
      });
    }

    // Display competitor sources if available
    if (result.sources && result.sources.competitors && result.sources.competitors.length > 0) {
      console.log('\nCompetitor Sources:');
      result.sources.competitors.forEach((source: string, index: number) => {
        console.log(`${index + 1}. ${source}`);
      });
    }

    console.log('\nFollow-up Questions:');
    result.followUpQuestions.forEach((question: string, index: number) => {
      console.log(`${index + 1}. ${question}`);
    });

    console.log('\nTest completed successfully!');

    // Save the result to a file for reference (both JSON and Markdown)
    fs.writeFileSync('gemini-enhanced-analysis-result.json', JSON.stringify(result, null, 2));

    // Create a nicely formatted Markdown file
    const markdownContent = generateMarkdownReport(result);
    fs.writeFileSync('pitch-deck-analysis.md', markdownContent);

    // Extract the Notion data directly from the AI response
    const notionData = result.notionData;
    fs.writeFileSync('notion-import.json', JSON.stringify(notionData, null, 2));

    console.log('Analysis result saved to:');
    console.log('- gemini-enhanced-analysis-result.json (raw data)');
    console.log('- pitch-deck-analysis.md (formatted report)');
    console.log('- notion-import.json (Notion-compatible data)');

  } catch (error) {
    console.error('Error testing enhanced Gemini service:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testEnhancedGeminiService();
