#!/bin/bash

# Script to enable the Vertex AI API for the project

# Get the project ID from environment variable or .env file
if [ -z "$GOOGLE_CLOUD_PROJECT" ]; then
  if [ -f .env ]; then
    source <(grep -v '^#' .env | sed -E 's/(.*)=.*/export \1/')
  fi
fi

PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-valurize-app}

echo "Enabling Vertex AI API for project: $PROJECT_ID"
gcloud services enable aiplatform.googleapis.com --project=$PROJECT_ID

echo "Checking API status..."
gcloud services list --enabled --filter=aiplatform.googleapis.com --project=$PROJECT_ID

echo ""
echo "If the API was successfully enabled, you should see 'aiplatform.googleapis.com' in the list above."
echo "If not, please visit the following URL to enable it manually:"
echo "https://console.developers.google.com/apis/api/aiplatform.googleapis.com/overview?project=$PROJECT_ID"
echo ""
echo "After enabling the API, wait a few minutes for the change to propagate before running your test again."
