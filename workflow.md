# Valurize Platform Workflow

This document provides a detailed analysis of the Valurize platform's architecture, components, and workflow. Valurize is a multi-tenant, scalable pitch deck analysis platform for VCs deployed on Google Cloud Platform (GCP).

## 1. System Architecture Overview

The Valurize platform is built using a microservices architecture deployed on Google Cloud Platform. The system consists of the following core services:

### 1.1 Core Services

1. **Tenant API Service**
   - Manages tenant (VC firm) information, configurations, and quotas
   - Handles billing and subscription management
   - Enforces usage limits based on subscription tier

2. **Admin API Service**
   - Provides administrative functions for platform management
   - Manages user accounts and permissions
   - Provides analytics and reporting capabilities

3. **Slack Ingest Service**
   - Handles OAuth integration with Slack
   - Processes pitch deck PDFs uploaded via Slack
   - Manages Slack commands and interactions
   - Queues PDFs for processing

4. **Email Ingest Service**
   - Processes pitch deck PDFs received via email
   - Integrates with Mailgun for email handling
   - Identifies tenants based on email addresses
   - Queues PDFs for processing

5. **Processor Service**
   - Analyzes pitch decks using Gemini 2.5 Pro Preview
   - Processes PDFs sequentially with concurrency=1
   - Generates detailed analysis with scores and recommendations
   - Stores results in Cloud Storage and Firestore

6. **Slack Notify Service**
   - Sends analysis results back to Slack
   - Formats messages for optimal readability
   - Includes summary, scores, and links to detailed analysis

7. **Notion Sync Service**
   - Integrates with Notion via OAuth
   - Syncs analysis results to customer Notion databases
   - Maps analysis fields to Notion database properties
   - Filters results based on score thresholds

8. **Token Rotator Service**
   - Manages and refreshes OAuth tokens for Slack and Notion
   - Ensures continuous access to third-party services
   - Handles token expiration and renewal

### 1.2 GCP Resources

The platform utilizes the following GCP resources:

- **Cloud Run**: Hosts all microservices with appropriate scaling configurations
- **Cloud Storage**: Stores PDFs and analysis results
- **Firestore**: Stores tenant data, batch information, and system state
- **Pub/Sub**: Handles asynchronous communication between services
- **Secret Manager**: Securely stores API keys and OAuth tokens
- **Cloud Scheduler**: Triggers periodic tasks like token rotation

## 2. Data Flow and Processing Pipeline

### 2.1 Ingestion Flow

#### 2.1.1 Slack Ingestion

1. User uploads a PDF to Slack or uses the `/uploaddeck` command
2. Slack Ingest service receives the file via the Slack Events API
3. The service:
   - Validates the file is a PDF
   - Downloads the file from Slack
   - Uploads it to Cloud Storage (`{PROJECT_ID}-pdfs` bucket)
   - Creates a batch record in Firestore with status "pending"
   - Publishes a message to the "pdf-processing" Pub/Sub topic

#### 2.1.2 Email Ingestion

1. User emails a PDF to a designated email address
2. Mailgun forwards the email to the Email Ingest service webhook
3. The service:
   - Extracts attachments from the email
   - Validates PDFs and filters out other file types
   - Identifies the tenant based on sender email
   - Uploads PDFs to Cloud Storage
   - Creates batch records in Firestore
   - Publishes a message to the "pdf-processing" Pub/Sub topic
   - Sends a confirmation email to the sender

### 2.2 Processing Flow

1. Processor service receives message from "pdf-processing" Pub/Sub subscription
2. For each batch ID in the message:
   - Updates batch status to "processing"
   - Retrieves tenant information from Firestore
   - Downloads the PDF from Cloud Storage
   - Analyzes the PDF using Gemini 2.5 Pro Preview
   - Extracts key information (company name, score, etc.)
   - Saves detailed analysis as Markdown to Cloud Storage
   - Saves structured analysis data as JSON to Cloud Storage
   - Updates batch record in Firestore with results
   - Updates usage metrics for the tenant
3. After processing all batches, publishes a message to "analysis-complete" Pub/Sub topic

### 2.3 Notification Flow

1. Slack Notify service receives message from "analysis-complete" Pub/Sub subscription
2. For each completed batch:
   - Retrieves the analysis result from Cloud Storage
   - Formats the result for Slack using the slackFormatter utility
   - Retrieves the tenant's Slack token from Secret Manager
   - Sends a formatted message to the original Slack channel
   - The message includes:
     - Company name and score
     - Executive summary
     - Key strengths and weaknesses
     - Investment recommendation
     - Link to detailed memo

### 2.4 Notion Sync Flow

1. Notion Sync service receives message from "analysis-complete" Pub/Sub subscription
2. For each completed batch:
   - Checks if the tenant has Notion integration enabled
   - Verifies the tenant has selected a database
   - Retrieves the tenant's Notion token from Secret Manager
   - Checks if the batch score meets the tenant's threshold
   - Retrieves the analysis result from Cloud Storage
   - Maps the analysis fields to Notion database properties
   - Creates a new page in the Notion database
   - Updates the batch record with Notion sync status

## 3. Authentication and Authorization

### 3.1 Slack Integration

1. Tenant initiates Slack integration via admin interface
2. Slack Ingest service handles OAuth flow:
   - Redirects to Slack authorization page
   - Receives authorization code
   - Exchanges code for access token
   - Stores token in Secret Manager
   - Updates tenant record with Slack connection status

### 3.2 Notion Integration

1. User initiates Notion integration via Slack command `/connect-notion`
2. Notion Sync service handles OAuth flow:
   - Redirects to Notion authorization page
   - Receives authorization code
   - Exchanges code for access token
   - Stores token in Secret Manager
   - Updates tenant record with Notion connection status
3. User selects a Notion database for syncing results
4. User maps analysis fields to database properties

### 3.3 Token Management

1. Token Rotator service periodically refreshes OAuth tokens
2. For each tenant with integrations:
   - Retrieves current token from Secret Manager
   - Checks if refresh is needed
   - Calls appropriate API to refresh token
   - Stores new token in Secret Manager
   - Updates tenant record if needed

## 4. Multi-Tenant Architecture

### 4.1 Tenant Isolation

- Each tenant has a unique ID (typically their Slack team ID)
- PDFs are stored in tenant-specific paths in Cloud Storage
- Analysis results are stored in tenant-specific paths
- Firestore collections use tenant IDs as document IDs or fields
- OAuth tokens are stored in tenant-specific secrets

### 4.2 Tenant Configuration

- Tenants can customize their analysis prompt
- Tenants can set score thresholds for Notion sync
- Tenants can map analysis fields to Notion database properties
- Tenants have usage quotas based on subscription tier

## 5. Key Components Deep Dive

### 5.1 Gemini Analysis Service

The core of the platform is the Gemini-powered analysis service that evaluates pitch decks. Key features:

- Uses Gemini 2.5 Pro Preview model via Vertex AI
- Implements a comprehensive prompt that evaluates:
  - Team (25% weight)
  - Market (20% weight)
  - Product (20% weight)
  - Traction (15% weight)
  - Business Model (10% weight)
  - Financials (10% weight)
  - Outlier Potential (5% weight)
- Cross-checks information with external sources
- Generates a weighted score on a 0-100 scale
- Provides investment recommendations:
  - High Priority (Score >= 4.0)
  - Maybe (Score >= 3.0 and < 4.0)
  - Pass (Score < 3.0)
- Outputs detailed analysis in structured JSON format
- Includes data for both Notion and Slack in a single response

### 5.2 Batch Processing

The platform implements a robust batch processing system:

- PDFs are processed sequentially to ensure consistent quality
- Processor service has concurrency=1 to prevent overloading Gemini API
- Batches have statuses: pending, processing, completed, failed
- Failed batches can be retried manually
- Batch processing can be triggered by:
  - Pub/Sub messages (normal flow)
  - Manual API calls (for testing or retries)
  - Scheduled jobs (for batch processing)

### 5.3 Error Handling and Resilience

The platform implements several error handling mechanisms:

- Failed Gemini API calls return a default analysis with error indicators
- Pub/Sub messages can be nacked for temporary errors to trigger retries
- Permanent errors are logged and acknowledged to prevent infinite retries
- Batch status is updated to reflect errors
- Token refresh failures are tracked and reported

## 6. Deployment Configuration

### 6.1 Service Configurations

Each service is deployed to Cloud Run with specific configurations:

- **Processor Service**: 2 vCPU, 2GB memory, concurrency=1
- **Ingest Services**: 0.5 vCPU, 512MB memory, concurrency=50
- **Notify/Sync Services**: 0.5 vCPU, 512MB memory, concurrency=10
- **API Services**: 1 vCPU, 1GB memory, concurrency=10-20
- **Token Rotator**: 0.2 vCPU, 256MB memory, concurrency=1

### 6.2 Scaling Parameters

- Minimum instances: 1 for all services
- Maximum instances vary by service:
  - Processor: 100 instances
  - Ingest: 200 instances
  - Notify/Sync: 20 instances
  - APIs: 10-20 instances
  - Token Rotator: 1 instance

## 7. Security Considerations

### 7.1 Authentication

- All services use Firebase Admin SDK with application default credentials in production
- OAuth tokens are stored in Secret Manager, not in Firestore
- API keys and secrets are stored in environment variables or Secret Manager

### 7.2 API Security

- All services implement helmet.js for HTTP security headers
- CORS is configured appropriately for web interfaces
- Slack webhook verification is implemented
- Mailgun signature verification is implemented

### 7.3 Data Protection

- PDFs and analysis results are stored in tenant-specific paths
- Tenant data is isolated in Firestore
- OAuth tokens are stored securely in Secret Manager
- Original PDFs can be deleted after processing if configured

## 8. Monitoring and Observability

### 8.1 Logging

- All services use a structured logger
- Log levels are configurable
- Key events are logged:
  - Service startup/shutdown
  - Batch processing status changes
  - Integration events
  - Errors and warnings

### 8.2 Health Checks

- All services expose a `/health` endpoint
- Health checks return HTTP 200 with `{ status: "ok" }`
- Cloud Run uses these endpoints for health monitoring

## 9. Integration Points

### 9.1 Slack Integration

- **OAuth URL**: `https://slack-ingest-[PROJECT_ID].a.run.app/slack/oauth_redirect`
- **Events URL**: `https://slack-ingest-[PROJECT_ID].a.run.app/slack/events`
- **Interactive Components URL**: `https://slack-ingest-[PROJECT_ID].a.run.app/slack/interactive`
- **Commands URL**: `https://slack-ingest-[PROJECT_ID].a.run.app/slack/commands`

### 9.2 Notion Integration

- **OAuth Callback URL**: `https://notion-sync-[PROJECT_ID].a.run.app/notion/callback`

### 9.3 Email Integration

- **Mailgun Webhook URL**: `https://email-ingest-[PROJECT_ID].a.run.app/webhook`

## 10. Conclusion

The Valurize platform is a sophisticated, multi-tenant system that leverages modern cloud architecture and AI capabilities to provide valuable pitch deck analysis for VCs. The microservices architecture ensures scalability, resilience, and maintainability, while the integration with Slack and Notion provides a seamless user experience.
