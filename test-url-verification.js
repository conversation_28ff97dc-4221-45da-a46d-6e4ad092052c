const crypto = require('crypto');
const axios = require('axios');

// Function to create a Slack signature
function createSlackSignature(signingSecret, timestamp, body) {
  const baseString = `v0:${timestamp}:${body}`;
  const signature = crypto.createHmac('sha256', signingSecret)
    .update(baseString)
    .digest('hex');
  return `v0=${signature}`;
}

// Slack signing secret
const signingSecret = '744129b58144af51b9b0690dc2177ca1';

// Create a URL verification payload
const payload = {
  type: 'url_verification',
  challenge: '3eZbrw1aBm2rZgRNFdxV2595E9CY3gmdALWMmHkvFXO7tYXAYM8P',
  token: 'Jhj5dZrVaK7ZwHHjRyZWjbDl'
};

// Convert payload to JSON string
const body = JSON.stringify(payload);

// Create timestamp (current time in seconds)
const timestamp = Math.floor(Date.now() / 1000);

// Create signature
const signature = createSlackSignature(signingSecret, timestamp, body);

// Make the request
axios.post('https://slack-ingest-1037842568074.us-central1.run.app/slack/events', body, {
  headers: {
    'Content-Type': 'application/json',
    'X-Slack-Request-Timestamp': timestamp,
    'X-Slack-Signature': signature
  }
})
  .then(response => {
    console.log('Response:', response.data);
  })
  .catch(error => {
    console.error('Error:', error.response ? error.response.data : error.message);
  });
