# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.npm
.yarn

# TypeScript
dist/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# OS specific
.DS_Store
Thumbs.db

# IDE
.idea/
.vscode/
*.swp
*.swo

# Terraform
.terraform/
*.tfstate
*.tfstate.*
*.tfvars
.terraformrc
terraform.rc

# GCP
*-credentials.json
*-key.json

# Testing
coverage/
