#!/bin/bash

# Slack signing secret from your .env file
SLACK_SIGNING_SECRET="744129b58144af51b9b0690dc2177ca1"

# URL verification payload
PAYLOAD='{"type":"url_verification","challenge":"3eZbrw1aBm2rZgRNFdxV2595E9CY3gmdALWMmHkvFXO7tYXAYM8P","token":"Jhj5dZrVaK7ZwHHjRyZWjbDl"}'

# Current timestamp in seconds
TIMESTAMP=$(date +%s)

# Create the signature base string
BASE_STRING="v0:${TIMESTAMP}:${PAYLOAD}"

# Generate the signature
SIGNATURE=$(echo -n "${BASE_STRING}" | openssl sha256 -hmac "${SLACK_SIGNING_SECRET}" | sed 's/^.* //')
SLACK_SIGNATURE="v0=${SIGNATURE}"

# Make the curl request
echo "Sending request to https://slack-ingest-1037842568074.us-central1.run.app/slack/events"
echo "Payload: ${PAYLOAD}"
echo "Timestamp: ${TIMESTAMP}"
echo "Signature: ${SLACK_SIGNATURE}"
echo ""

curl -v -X POST \
  -H "Content-Type: application/json" \
  -H "X-Slack-Request-Timestamp: ${TIMESTAMP}" \
  -H "X-Slack-Signature: ${SLACK_SIGNATURE}" \
  -d "${PAYLOAD}" \
  https://slack-ingest-1037842568074.us-central1.run.app/slack/events
