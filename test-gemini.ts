import { analyzeDocument } from './src/services/geminiService';
import * as fs from 'fs';
import * as path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Simple test for the Gemini service
 * This test loads a sample PDF and sends it to Gemini for analysis
 */
async function testGeminiService() {
  try {
    console.log('Starting Gemini service test...');
    
    // Path to a sample PDF file (replace with an actual PDF path)
    const pdfPath = process.env.TEST_PDF_PATH || './sample.pdf';
    
    if (!fs.existsSync(pdfPath)) {
      console.error(`Error: Test PDF file not found at ${pdfPath}`);
      console.log('Please set TEST_PDF_PATH environment variable to a valid PDF file path');
      return;
    }
    
    // Load the PDF file
    console.log(`Loading PDF from ${pdfPath}...`);
    const fileBuffer = fs.readFileSync(pdfPath);
    
    // Custom prompt for testing
    const testPrompt = `
    Analyze this pitch deck with the following criteria:
    
    1. Team (25%): Evaluate the founding team's experience, domain expertise, and track record.
    2. Market (20%): Assess market size, growth potential, and competitive landscape.
    3. Product (20%): Evaluate product-market fit, innovation, and technical feasibility.
    4. Traction (15%): Analyze current customers, revenue, and growth metrics.
    5. Business Model (10%): Evaluate revenue model, unit economics, and scalability.
    6. Financials (10%): Assess financial projections, funding needs, and use of funds.
    
    Be critical but fair in your assessment.
    `;
    
    console.log('Sending PDF to Gemini for analysis...');
    const startTime = Date.now();
    
    // Call the Gemini service
    const result = await analyzeDocument(fileBuffer, testPrompt);
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log(`Analysis completed in ${processingTime.toFixed(2)} seconds`);
    console.log('\nAnalysis Results:');
    console.log('=================');
    console.log(`Company: ${result.companyName}`);
    console.log(`Score: ${result.score}/100`);
    console.log(`Summary: ${result.summary}`);
    
    console.log('\nStrengths:');
    result.strengths.forEach((strength, index) => {
      console.log(`${index + 1}. ${strength}`);
    });
    
    console.log('\nWeaknesses:');
    result.weaknesses.forEach((weakness, index) => {
      console.log(`${index + 1}. ${weakness}`);
    });
    
    console.log('\nMarket Opportunity:');
    console.log(`Size: ${result.marketOpportunity.size}`);
    console.log(`Growth: ${result.marketOpportunity.growth}`);
    console.log(`Assessment: ${result.marketOpportunity.assessment}`);
    
    console.log('\nTeam:');
    console.log(`Assessment: ${result.team.assessment}`);
    console.log(`Experience: ${result.team.experience}`);
    console.log(`Gaps: ${result.team.gaps}`);
    
    console.log('\nFinancials:');
    console.log(`Assessment: ${result.financials.assessment}`);
    console.log(`Runway: ${result.financials.runway}`);
    console.log(`Projections: ${result.financials.projections}`);
    
    console.log('\nInvestment Recommendation:');
    console.log(result.investmentRecommendation);
    
    console.log('\nFollow-up Questions:');
    result.followUpQuestions.forEach((question, index) => {
      console.log(`${index + 1}. ${question}`);
    });
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing Gemini service:', error);
  }
}

// Run the test
testGeminiService();
