#!/bin/bash

# Deploy script for Valurize app (token-rotator and slack-ingest)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying services to project: ${PROJECT_ID}, region: ${REGION}"

# Build shared package first
echo "Building shared package..."
cd services/shared
npm install
npm run build
cd ../..

# Deploy Token Rotator
echo "Deploying Token Rotator..."
./scripts/deploy-token-rotator.sh

# Deploy Slack Ingest
echo "Deploying Slack Ingest..."
./scripts/deploy-slack-ingest.sh

echo "Deployment completed successfully!"
