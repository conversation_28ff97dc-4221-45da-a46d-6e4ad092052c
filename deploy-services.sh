#!/bin/bash

# Deploy script for Valurize app (token-rotator and slack-ingest)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying services to project: ${PROJECT_ID}, region: ${REGION}"

# Deploy Token Rotator
echo "Deploying Token Rotator..."
cd services

# Copy shared package to token-rotator directory
echo "Copying shared package to token-rotator directory..."
mkdir -p token-rotator/shared
cp -r shared/* token-rotator/shared/

# Build and push the token-rotator image
echo "Building token-rotator Docker image..."
cp token-rotator/Dockerfile.new token-rotator/Dockerfile
gcloud builds submit --tag gcr.io/${PROJECT_ID}/token-rotator \
  --project ${PROJECT_ID} \
  token-rotator

# Deploy to Cloud Run
echo "Deploying token-rotator to Cloud Run..."
gcloud run deploy token-rotator \
  --image gcr.io/${PROJECT_ID}/token-rotator \
  --platform managed \
  --region ${REGION} \
  --no-allow-unauthenticated \
  --memory 256Mi \
  --cpu 0.2 \
  --concurrency 1 \
  --min-instances 0 \
  --max-instances 1 \
  --set-env-vars="SLACK_CLIENT_ID=${SLACK_CLIENT_ID},SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET},SLACK_STATE_SECRET=${SLACK_STATE_SECRET:-valurize-state-secret},GOOGLE_CLOUD_PROJECT=${PROJECT_ID},LOG_LEVEL=${LOG_LEVEL:-info},TOKEN_ROTATE_SUBSCRIPTION=${TOKEN_ROTATE_SUBSCRIPTION:-token-rotate-subscription},NOTION_CLIENT_ID=${NOTION_CLIENT_ID},NOTION_CLIENT_SECRET=${NOTION_CLIENT_SECRET},NODE_ENV=production"

# Deploy Slack Ingest
echo "Deploying Slack Ingest..."

# Copy shared package to slack-ingest directory
echo "Copying shared package to slack-ingest directory..."
mkdir -p slack-ingest/shared
cp -r shared/* slack-ingest/shared/

# Build and push the slack-ingest image
echo "Building slack-ingest Docker image..."
cp slack-ingest/Dockerfile.new slack-ingest/Dockerfile
gcloud builds submit --tag gcr.io/${PROJECT_ID}/slack-ingest \
  --project ${PROJECT_ID} \
  slack-ingest

# Deploy to Cloud Run
echo "Deploying slack-ingest to Cloud Run..."
gcloud run deploy slack-ingest \
  --image gcr.io/${PROJECT_ID}/slack-ingest \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 50 \
  --min-instances 1 \
  --max-instances 100 \
  --set-env-vars="SLACK_SIGNING_SECRET=${SLACK_SIGNING_SECRET},SLACK_CLIENT_ID=${SLACK_CLIENT_ID},SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET},SLACK_STATE_SECRET=${SLACK_STATE_SECRET:-valurize-state-secret},GOOGLE_CLOUD_PROJECT=${PROJECT_ID},LOG_LEVEL=${LOG_LEVEL:-info},PDF_BUCKET_NAME=${PDF_BUCKET_NAME:-${PROJECT_ID}-pdfs},PDF_PROCESSING_TOPIC=${PDF_PROCESSING_TOPIC:-pdf-processing},SERVICE_URL=https://slack-ingest-${PROJECT_ID}.a.run.app,NODE_ENV=production"

cd ..

echo "Deployment completed successfully!"
