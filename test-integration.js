/**
 * Test script to verify that the analysis result from test-pdf-analysis.js
 * is compatible with the Slack and Notion services.
 */

const fs = require('fs');

// Load the analysis result
let analysis;
try {
  const analysisJson = fs.readFileSync('test-analysis-result.json', 'utf8');
  analysis = JSON.parse(analysisJson);
  console.log('Loaded analysis result from test-analysis-result.json');
} catch (error) {
  console.error(`Error loading analysis result: ${error}`);
  console.log('Please run test-pdf-analysis.js first to generate the analysis result');
  process.exit(1);
}

// Test Slack formatting
console.log('\n=== Testing Slack Formatting ===');
const slackMessage = formatSlackMessage(analysis, 'mock-batch-id');
console.log(`Generated Slack message with ${slackMessage.length} blocks`);

// Check for required fields
const requiredSlackFields = [
  'companyName', 'score', 'summary', 'strengths', 'weaknesses', 
  'keyWatchouts', 'followUpQuestions', 'investmentRecommendation',
  'teamScore', 'marketScore', 'tractionScore', 'outlierPotentialScore'
];

const missingSlackFields = requiredSlackFields.filter(field => {
  if (field.includes('.')) {
    const [parent, child] = field.split('.');
    return !analysis[parent] || analysis[parent][child] === undefined;
  }
  return analysis[field] === undefined;
});

if (missingSlackFields.length > 0) {
  console.log(`⚠️ Missing fields for Slack: ${missingSlackFields.join(', ')}`);
} else {
  console.log('✅ All required fields for Slack are present');
}

// Test Notion formatting
console.log('\n=== Testing Notion Formatting ===');
const notionPage = createNotionPage(analysis);
console.log(`Generated Notion page with ${Object.keys(notionPage.properties).length} properties`);

// Check for required fields
const requiredNotionFields = [
  'companyName', 'score', 'summary', 'strengths', 'weaknesses',
  'marketOpportunity.assessment', 'team.assessment', 'financials.assessment',
  'investmentRecommendation', 'followUpQuestions'
];

const missingNotionFields = requiredNotionFields.filter(field => {
  if (field.includes('.')) {
    const [parent, child] = field.split('.');
    return !analysis[parent] || analysis[parent][child] === undefined;
  }
  return analysis[field] === undefined;
});

if (missingNotionFields.length > 0) {
  console.log(`⚠️ Missing fields for Notion: ${missingNotionFields.join(', ')}`);
} else {
  console.log('✅ All required fields for Notion are present');
}

// Check for notionData field
if (analysis.notionData) {
  console.log('✅ notionData field is present');
} else {
  console.log('⚠️ notionData field is missing');
}

console.log('\n=== Overall Assessment ===');
if (missingSlackFields.length === 0 && missingNotionFields.length === 0) {
  console.log('✅ Analysis result is compatible with both Slack and Notion services');
} else {
  console.log('⚠️ Analysis result is missing some fields required by the services');
}

/**
 * Formats an analysis result into a Slack message (simplified version)
 *
 * @param analysis The analysis result
 * @param batchId The batch ID
 * @returns Slack blocks for the message
 */
function formatSlackMessage(analysis, batchId) {
  // Format strengths and weaknesses (limit to top 3 for better readability)
  const strengthsList = analysis.strengths.slice(0, 3).map(s => `• ${s}`).join('\n');
  const weaknessesList = analysis.weaknesses.slice(0, 3).map(w => `• ${w}`).join('\n');

  // Format key watchouts
  const watchoutsList = analysis.keyWatchouts && analysis.keyWatchouts.length > 0
    ? analysis.keyWatchouts.map(w => `• ${w}`).join('\n')
    : "No significant watchouts identified.";

  // Format follow-up questions (limit to top 3)
  const questionsList = analysis.followUpQuestions.slice(0, 3).map(q => `• ${q}`).join('\n');

  // Create blocks (simplified version)
  return [
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `${analysis.companyName} Analysis Complete`,
        emoji: true,
      },
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Score: ${analysis.score}/100*  |  *Recommendation: ${analysis.investmentRecommendation}*`,
      },
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Summary*\n${analysis.summary}`,
      },
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Key Strengths*\n${strengthsList}`,
        },
        {
          type: 'mrkdwn',
          text: `*Key Weaknesses*\n${weaknessesList}`,
        },
      ],
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Key Watchouts*\n${watchoutsList}`,
      },
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Team Score*\n${analysis.teamScore}/5.0`,
        },
        {
          type: 'mrkdwn',
          text: `*Market Score*\n${analysis.marketScore}/5.0`,
        },
      ],
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Traction Score*\n${analysis.tractionScore}/5.0`,
        },
        {
          type: 'mrkdwn',
          text: `*Outlier Potential*\n${analysis.outlierPotentialScore}/5.0`,
        },
      ],
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Follow-up Questions*\n${questionsList}`,
      },
    },
    // Add a button to view the detailed memo if available
    analysis.memoUrl ? {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '*Detailed Analysis*',
      },
      accessory: {
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'View Full Memo',
          emoji: true,
        },
        url: analysis.memoUrl,
        action_id: 'view_memo',
      },
    } : null,
  ].filter(Boolean); // Remove null blocks
}

/**
 * Creates a Notion page properties object (simplified version)
 * 
 * @param analysis Analysis result
 * @returns The Notion page properties
 */
function createNotionPage(analysis) {
  // Map fields according to tenant configuration
  const properties = {};
  
  // Default mappings
  properties['Name'] = { title: [{ text: { content: analysis.companyName || 'Unnamed Company' } }] };
  properties['Score'] = { number: analysis.score || 0 };
  properties['Summary'] = { rich_text: [{ text: { content: truncateText(analysis.summary || '', 2000) } }] };
  properties['Strengths'] = { rich_text: [{ text: { content: truncateText(analysis.strengths?.join(', ') || '', 2000) } }] };
  properties['Weaknesses'] = { rich_text: [{ text: { content: truncateText(analysis.weaknesses?.join(', ') || '', 2000) } }] };
  properties['Recommendation'] = { rich_text: [{ text: { content: truncateText(analysis.investmentRecommendation || '', 2000) } }] };
  properties['Market'] = { rich_text: [{ text: { content: truncateText(analysis.marketOpportunity?.assessment || '', 2000) } }] };
  properties['Team'] = { rich_text: [{ text: { content: truncateText(analysis.team?.assessment || '', 2000) } }] };
  properties['Financials'] = { rich_text: [{ text: { content: truncateText(analysis.financials?.assessment || '', 2000) } }] };
  properties['Analyzed Date'] = { date: { start: new Date().toISOString() } };
  
  return {
    parent: { database_id: 'mock-database-id' },
    properties,
  };
}

/**
 * Truncates text to the specified length
 * 
 * @param text Text to truncate
 * @param maxLength Maximum length
 * @returns Truncated text
 */
function truncateText(text, maxLength) {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
}
