output "service_account_email" {
  description = "The email of the service account"
  value       = google_service_account.service_account.email
}

output "pdf_bucket_name" {
  description = "The name of the PDF bucket"
  value       = google_storage_bucket.pdf_bucket.name
}

output "results_bucket_name" {
  description = "The name of the results bucket"
  value       = google_storage_bucket.results_bucket.name
}

output "pdf_processing_topic" {
  description = "The name of the PDF processing topic"
  value       = google_pubsub_topic.pdf_processing.name
}

output "analysis_complete_topic" {
  description = "The name of the analysis complete topic"
  value       = google_pubsub_topic.analysis_complete.name
}

output "token_rotate_topic" {
  description = "The name of the token rotation topic"
  value       = google_pubsub_topic.token_rotate.name
}

output "pdf_processing_subscription" {
  description = "The name of the PDF processing subscription"
  value       = google_pubsub_subscription.pdf_processing_subscription.name
}

output "analysis_complete_subscription" {
  description = "The name of the analysis complete subscription"
  value       = google_pubsub_subscription.analysis_complete_subscription.name
}

output "token_rotate_subscription" {
  description = "The name of the token rotation subscription"
  value       = google_pubsub_subscription.token_rotate_subscription.name
}
