terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "services" {
  for_each = toset([
    "run.googleapis.com",
    "cloudscheduler.googleapis.com",
    "pubsub.googleapis.com",
    "firestore.googleapis.com",
    "storage.googleapis.com",
    "secretmanager.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com"
  ])
  project = var.project_id
  service = each.key

  disable_dependent_services = true
  disable_on_destroy         = false
}

# Create service account for Cloud Run services
resource "google_service_account" "service_account" {
  account_id   = "valurize-service-account"
  display_name = "Valurize Service Account"
  depends_on   = [google_project_service.services]
}

# Grant necessary roles to the service account
resource "google_project_iam_member" "service_account_roles" {
  for_each = toset([
    "roles/run.invoker",
    "roles/pubsub.editor",
    "roles/datastore.user",
    "roles/storage.admin",
    "roles/secretmanager.secretAccessor"
  ])
  project = var.project_id
  role    = each.key
  member  = "serviceAccount:${google_service_account.service_account.email}"
}

# Create GCS buckets
resource "google_storage_bucket" "pdf_bucket" {
  name          = "${var.project_id}-pdfs"
  location      = var.region
  force_destroy = true
  depends_on    = [google_project_service.services]
}

resource "google_storage_bucket" "results_bucket" {
  name          = "${var.project_id}-results"
  location      = var.region
  force_destroy = true
  depends_on    = [google_project_service.services]
}

# Create Pub/Sub topics
resource "google_pubsub_topic" "pdf_processing" {
  name       = "pdf-processing"
  depends_on = [google_project_service.services]
}

resource "google_pubsub_topic" "pdf_processing_dlq" {
  name       = "pdf-processing-dlq"
  depends_on = [google_project_service.services]
}

resource "google_pubsub_topic" "token_rotate" {
  name       = "token-rotate"
  depends_on = [google_project_service.services]
}

resource "google_pubsub_topic" "analysis_complete" {
  name       = "analysis-complete"
  depends_on = [google_project_service.services]
}

# Create Cloud Scheduler job for token rotation
resource "google_cloud_scheduler_job" "token_rotation" {
  name        = "token-rotation"
  description = "Rotate OAuth tokens"
  schedule    = "0 */11 * * *"
  time_zone   = "UTC"

  pubsub_target {
    topic_name = google_pubsub_topic.token_rotate.id
    data       = base64encode("{}")
  }

  depends_on = [google_project_service.services]
}

# Create Cloud Scheduler job for batch processing
resource "google_cloud_scheduler_job" "batch_processing" {
  name        = "batch-processing"
  description = "Process pending PDF batches"
  schedule    = "*/5 * * * *"  # Run every 5 minutes
  time_zone   = "UTC"

  http_target {
    uri         = "https://slack-ingest-${var.project_id}.a.run.app/process-batches"
    http_method = "POST"
    oidc_token {
      service_account_email = google_service_account.service_account.email
    }
  }

  depends_on = [google_project_service.services]
}

# Create Pub/Sub subscriptions
resource "google_pubsub_subscription" "pdf_processing_subscription" {
  name  = "pdf-processing-subscription"
  topic = google_pubsub_topic.pdf_processing.name

  ack_deadline_seconds = 600

  expiration_policy {
    ttl = ""
  }

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  depends_on = [google_project_service.services]
}

resource "google_pubsub_subscription" "analysis_complete_subscription" {
  name  = "analysis-complete-subscription"
  topic = google_pubsub_topic.analysis_complete.name

  ack_deadline_seconds = 600

  expiration_policy {
    ttl = ""
  }

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  depends_on = [google_project_service.services]
}

resource "google_pubsub_subscription" "token_rotate_subscription" {
  name  = "token-rotate-subscription"
  topic = google_pubsub_topic.token_rotate.name

  ack_deadline_seconds = 600

  expiration_policy {
    ttl = ""
  }

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  depends_on = [google_project_service.services]
}

# Create DLQ subscriptions
resource "google_pubsub_subscription" "pdf_processing_dlq_subscription" {
  name  = "pdf-processing-dlq-subscription"
  topic = google_pubsub_topic.pdf_processing_dlq.name

  ack_deadline_seconds = 600

  expiration_policy {
    ttl = ""
  }

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  depends_on = [google_project_service.services]
}

# Firestore database (Native mode)
resource "google_firestore_database" "database" {
  name        = "(default)"
  location_id = var.region
  type        = "FIRESTORE_NATIVE"
  depends_on  = [google_project_service.services]
}
