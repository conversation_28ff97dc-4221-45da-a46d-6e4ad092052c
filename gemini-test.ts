import { analyzeDocument } from './src/services/geminiService';
import * as fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Test the Gemini 2.5 Pro Preview service with a real API call
 * This test uses the PDF from the test folder and calls the actual Gemini API
 */
async function testGeminiService() {
  try {
    console.log('Starting Gemini 2.5 Pro Preview test...');
    
    // Path to the PDF file in the test folder
    const pdfPath = './test/Airbnb.pdf';
    
    if (!fs.existsSync(pdfPath)) {
      console.error(`Error: Test PDF file not found at ${pdfPath}`);
      return;
    }
    
    // Load the PDF file
    console.log(`Loading PDF from ${pdfPath}...`);
    const fileBuffer = fs.readFileSync(pdfPath);
    console.log(`PDF loaded successfully. Size: ${(fileBuffer.length / 1024).toFixed(2)} KB`);
    
    // Custom prompt for testing - using the criteria mentioned in the documentation
    const testPrompt = `
    Analyze this pitch deck with the following criteria:
    
    1. Team (25%): Evaluate the founding team's experience, domain expertise, and track record.
    2. Market (20%): Assess market size, growth potential, and competitive landscape.
    3. Product (20%): Evaluate product-market fit, innovation, and technical feasibility.
    4. Traction (15%): Analyze current customers, revenue, and growth metrics.
    5. Business Model (10%): Evaluate revenue model, unit economics, and scalability.
    6. Financials (10%): Assess financial projections, funding needs, and use of funds.
    
    Be critical but fair in your assessment.
    `;
    
    console.log('Sending PDF to Gemini 2.5 Pro Preview for analysis...');
    console.log('This may take a minute or two as the model processes the document...');
    const startTime = Date.now();
    
    // Call the Gemini service
    const result = await analyzeDocument(fileBuffer, testPrompt);
    
    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;
    
    console.log(`\nAnalysis completed in ${processingTime.toFixed(2)} seconds`);
    console.log('\nAnalysis Results:');
    console.log('=================');
    console.log(`Company: ${result.companyName}`);
    console.log(`Score: ${result.score}/100`);
    console.log(`Summary: ${result.summary}`);
    
    console.log('\nStrengths:');
    result.strengths.forEach((strength, index) => {
      console.log(`${index + 1}. ${strength}`);
    });
    
    console.log('\nWeaknesses:');
    result.weaknesses.forEach((weakness, index) => {
      console.log(`${index + 1}. ${weakness}`);
    });
    
    console.log('\nMarket Opportunity:');
    console.log(`Size: ${result.marketOpportunity.size}`);
    console.log(`Growth: ${result.marketOpportunity.growth}`);
    console.log(`Assessment: ${result.marketOpportunity.assessment}`);
    
    console.log('\nTeam:');
    console.log(`Assessment: ${result.team.assessment}`);
    console.log(`Experience: ${result.team.experience}`);
    console.log(`Gaps: ${result.team.gaps}`);
    
    console.log('\nFinancials:');
    console.log(`Assessment: ${result.financials.assessment}`);
    console.log(`Runway: ${result.financials.runway}`);
    console.log(`Projections: ${result.financials.projections}`);
    
    console.log('\nInvestment Recommendation:');
    console.log(result.investmentRecommendation);
    
    console.log('\nFollow-up Questions:');
    result.followUpQuestions.forEach((question, index) => {
      console.log(`${index + 1}. ${question}`);
    });
    
    console.log('\nTest completed successfully!');
    
    // Save the result to a file for reference
    fs.writeFileSync('gemini-analysis-result.json', JSON.stringify(result, null, 2));
    console.log('Analysis result saved to gemini-analysis-result.json');
    
  } catch (error) {
    console.error('Error testing Gemini service:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    
    if (error instanceof Error && error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testGeminiService();
