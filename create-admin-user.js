const { initializeApp, applicationDefault, cert } = require('firebase-admin/app');
const { getFirestore } = require('firebase-admin/firestore');

// Initialize Firebase Admin
// Try to use applicationDefault() first, then fall back to service account if available
try {
  initializeApp({
    credential: applicationDefault(),
    projectId: process.env.GOOGLE_CLOUD_PROJECT || 'valurize-app',
  });
  console.log('Initialized Firebase app with applicationDefault credentials');
} catch (error) {
  console.error('Failed to initialize with applicationDefault:', error);
  
  if (process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PRIVATE_KEY) {
    try {
      initializeApp({
        credential: cert({
          projectId: process.env.GOOGLE_CLOUD_PROJECT || 'valurize-app',
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        }),
      });
      console.log('Initialized Firebase app with service account credentials');
    } catch (error) {
      console.error('Failed to initialize with service account:', error);
      process.exit(1);
    }
  } else {
    console.error('No Firebase credentials available. Set GOOGLE_APPLICATION_CREDENTIALS or provide service account details.');
    process.exit(1);
  }
}

const db = getFirestore();

async function createAdminUser() {
  try {
    // Check if admin user already exists
    console.log('Checking if admin user exists...');
    const adminUsersSnapshot = await db.collection('ADMIN_USERS')
      .where('email', '==', '<EMAIL>')
      .limit(1)
      .get();
    
    if (!adminUsersSnapshot.empty) {
      console.log('Admin user already exists.');
      
      // Get the admin user document
      const adminUserDoc = adminUsersSnapshot.docs[0];
      const adminUserData = adminUserDoc.data();
      
      console.log('Admin user details:');
      console.log(`- ID: ${adminUserDoc.id}`);
      console.log(`- Email: ${adminUserData.email}`);
      console.log(`- Role: ${adminUserData.role}`);
      console.log(`- Password: ${adminUserData.password}`);
      
      // Update the admin user password if it's not 'admin123'
      if (adminUserData.password !== 'admin123') {
        console.log('Updating admin user password to "admin123"...');
        await db.collection('ADMIN_USERS').doc(adminUserDoc.id).update({
          password: 'admin123',
          updatedAt: new Date()
        });
        console.log('Admin user password updated successfully.');
      }
      
      return;
    }
    
    // Create admin user
    console.log('Creating admin user...');
    const now = new Date();
    
    const newAdminRef = await db.collection('ADMIN_USERS').add({
      email: '<EMAIL>',
      password: 'admin123', // In a real application, you would hash this password
      role: 'admin',
      createdAt: now,
      updatedAt: now,
    });
    
    console.log(`Admin user created successfully with ID: ${newAdminRef.id}`);
  } catch (error) {
    console.error('Error creating/checking admin user:', error);
  }
}

// Execute the function
createAdminUser()
  .then(() => {
    console.log('Script completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
