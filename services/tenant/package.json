{"name": "tenant-api", "version": "1.0.0", "description": "Tenant management API for Valurize", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "firebase-admin": "^11.9.0", "@google-cloud/secret-manager": "^4.2.2", "joi": "^17.9.2", "winston": "^3.9.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/jest": "^29.5.2", "@types/node": "^20.3.3", "jest": "^29.5.0", "nodemon": "^2.0.22", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": [], "author": "", "license": "ISC"}