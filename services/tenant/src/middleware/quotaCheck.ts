import { Request, Response, NextFunction } from 'express';
import { db } from '../index';
import { AppError } from './errorHandler';
import { logger } from '../utils/logger';
import { TenantStatus } from '../types/tenant';

export const quotaCheck = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Skip quota check for GET requests
    if (req.method === 'GET') {
      return next();
    }

    const teamId = req.params.teamId;
    if (!teamId) {
      return next();
    }

    // Get tenant information
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();
    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }

    const tenant = tenantDoc.data();
    if (!tenant) {
      return next(new AppError('Tenant data not found', 404));
    }

    // Check tenant status
    const tenantStatus = tenant.status || TenantStatus.ACTIVE;

    // If tenant is paused or deleted, deny access
    if (tenantStatus === TenantStatus.PAUSED) {
      logger.warn(`Access denied for paused tenant ${teamId}`);
      return next(new AppError('Tenant is currently paused', 403));
    }

    if (tenantStatus === TenantStatus.DELETED) {
      logger.warn(`Access denied for deleted tenant ${teamId}`);
      return next(new AppError('Tenant has been deleted', 403));
    }

    // Calculate current billing cycle
    const joinDate = tenant.joinDate.toDate();
    const now = new Date();
    const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Get usage for current cycle
    const usageDoc = await db
      .collection('USAGE')
      .doc(`${teamId}_${cycleStart.getFullYear()}_${cycleStart.getMonth() + 1}`)
      .get();

    const usage = usageDoc.exists ? usageDoc.data()?.count || 0 : 0;
    const tierLimit = tenant.tier === 'free' ? 10 :
                     tenant.tier === 'pro' ? 100 :
                     tenant.tier === 'enterprise' ? 1000 : 0;

    // Check if usage exceeds limit
    if (usage >= tierLimit) {
      logger.warn(`Quota exceeded for tenant ${teamId}`, {
        tier: tenant.tier,
        usage,
        limit: tierLimit,
      });

      return next(new AppError('Quota exceeded for current billing cycle', 429));
    }

    next();
  } catch (error) {
    logger.error(`Error in quota check: ${error}`);
    next(error);
  }
};
