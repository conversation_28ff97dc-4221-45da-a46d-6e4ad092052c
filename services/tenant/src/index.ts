import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { initializeApp, cert, applicationDefault } from 'firebase-admin/app';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { logger } from './utils/logger';
import tenantRoutes from './routes/tenant';
import billingRoutes from './routes/billing';
import { errorHandler } from './middleware/errorHandler';
import { quotaCheck } from './middleware/quotaCheck';

// Load environment variables
dotenv.config();

// Initialize Firebase Admin
initializeApp({
  // Use default credentials when running in GCP
  credential: process.env.NODE_ENV === 'production' ?
    applicationDefault() :
    cert({
      projectId: process.env.GOOGLE_CLOUD_PROJECT,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
});

// Initialize Firestore
export const db = getFirestore();
export { FieldValue };

// Create Express app
const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/tenant', quotaCheck, tenantRoutes);
app.use('/billing', billingRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Error handling middleware
app.use(errorHandler);

// Start server
app.listen(port, () => {
  logger.info(`Tenant API listening on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
