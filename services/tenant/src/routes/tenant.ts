import { Router } from 'express';
import { db } from '../index';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import Jo<PERSON> from 'joi';
import { TenantStatus } from '../types/tenant';

const router = Router();
const secretManager = new SecretManagerServiceClient();

// Validation schema for tenant creation/update
const tenantSchema = Joi.object({
  tier: Joi.string().valid('free', 'pro', 'enterprise').required(),
  prompt: Joi.string().max(1000),
  thresholds: Joi.object({
    notionSync: Joi.number().min(0).max(100).default(70),
  }),
  slackToken: Joi.string(),
  notionToken: Joi.string(),
  status: Joi.string().valid(
    TenantStatus.ACTIVE,
    TenantStatus.PAUSED,
    TenantStatus.DELETED
  ),
});

// Get tenant by ID
router.get('/:teamId', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }

    const tenant = tenantDoc.data();

    // Remove sensitive information
    if (tenant) {
      delete tenant.slackToken;
      delete tenant.notionToken;
    }

    res.status(200).json({
      status: 'success',
      data: tenant,
    });
  } catch (error) {
    logger.error(`Error getting tenant: ${error}`);
    next(error);
  }
});

// Create or update tenant
router.post('/:teamId', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    const { error, value } = tenantSchema.validate(req.body);

    if (error) {
      return next(new AppError(`Invalid request: ${error.message}`, 400));
    }

    const tenantData = value;
    const now = new Date();

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();
    const isNewTenant = !tenantDoc.exists;

    // Set join date and status for new tenants
    if (isNewTenant) {
      tenantData.joinDate = now;
      // Set default status to ACTIVE if not provided
      if (!tenantData.status) {
        tenantData.status = TenantStatus.ACTIVE;
      }
    }

    // Handle Slack token if provided
    if (tenantData.slackToken) {
      const secretName = `slack-token-${teamId}`;
      await createOrUpdateSecret(secretName, tenantData.slackToken);
      delete tenantData.slackToken;
    }

    // Handle Notion token if provided
    if (tenantData.notionToken) {
      const secretName = `notion-token-${teamId}`;
      await createOrUpdateSecret(secretName, tenantData.notionToken);
      delete tenantData.notionToken;
    }

    // Update tenant in Firestore
    await db.collection('TENANTS').doc(teamId).set(
      {
        ...tenantData,
        updatedAt: now,
      },
      { merge: true }
    );

    // Initialize usage record if new tenant
    if (isNewTenant) {
      const cycleKey = `${teamId}_${now.getFullYear()}_${now.getMonth() + 1}`;
      await db.collection('USAGE').doc(cycleKey).set({
        count: 0,
        lastUpdated: now,
      });
    }

    res.status(isNewTenant ? 201 : 200).json({
      status: 'success',
      message: isNewTenant ? 'Tenant created successfully' : 'Tenant updated successfully',
    });
  } catch (error) {
    logger.error(`Error creating/updating tenant: ${error}`);
    next(error);
  }
});

// Delete tenant
router.delete('/:teamId', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    const { soft = 'true' } = req.query; // Default to soft delete
    const isSoftDelete = soft === 'true';

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }

    if (isSoftDelete) {
      // Soft delete - update status to DELETED
      const now = new Date();
      await db.collection('TENANTS').doc(teamId).update({
        status: TenantStatus.DELETED,
        deletedAt: now,
        updatedAt: now,
      });

      res.status(200).json({
        status: 'success',
        message: 'Tenant soft-deleted successfully',
      });
    } else {
      // Hard delete - remove from database
      await db.collection('TENANTS').doc(teamId).delete();

      // Delete secrets
      try {
        await deleteSecret(`slack-token-${teamId}`);
        await deleteSecret(`notion-token-${teamId}`);
      } catch (error) {
        logger.warn(`Error deleting secrets for tenant ${teamId}: ${error}`);
      }

      res.status(200).json({
        status: 'success',
        message: 'Tenant permanently deleted successfully',
      });
    }
  } catch (error) {
    logger.error(`Error deleting tenant: ${error}`);
    next(error);
  }
});

// Helper function to create or update a secret
async function createOrUpdateSecret(secretName: string, secretValue: string) {
  const projectId = process.env.GOOGLE_CLOUD_PROJECT;
  const parent = `projects/${projectId}`;
  const secretPath = `${parent}/secrets/${secretName}`;

  try {
    // Check if secret exists
    try {
      await secretManager.getSecret({ name: secretPath });
    } catch (error) {
      // Create secret if it doesn't exist
      await secretManager.createSecret({
        parent,
        secretId: secretName,
        secret: {
          replication: {
            automatic: {},
          },
        },
      });
    }

    // Add new version
    await secretManager.addSecretVersion({
      parent: secretPath,
      payload: {
        data: Buffer.from(secretValue),
      },
    });
  } catch (error) {
    logger.error(`Error managing secret ${secretName}: ${error}`);
    throw error;
  }
}

// Helper function to delete a secret
async function deleteSecret(secretName: string) {
  const projectId = process.env.GOOGLE_CLOUD_PROJECT;
  const secretPath = `projects/${projectId}/secrets/${secretName}`;

  try {
    await secretManager.deleteSecret({ name: secretPath });
  } catch (error) {
    logger.error(`Error deleting secret ${secretName}: ${error}`);
    throw error;
  }
}

// Update tenant status
router.post('/:teamId/status', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    const { status } = req.body;

    if (!status) {
      return next(new AppError('Status is required', 400));
    }

    // Validate status
    if (!Object.values(TenantStatus).includes(status as TenantStatus)) {
      return next(new AppError(`Invalid status. Must be one of: ${Object.values(TenantStatus).join(', ')}`, 400));
    }

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }

    // Update tenant status
    const now = new Date();
    const updateData: Record<string, any> = {
      status,
      updatedAt: now,
    };

    // If status is DELETED, add deletedAt timestamp
    if (status === TenantStatus.DELETED) {
      updateData.deletedAt = now;
    } else if (tenantDoc.data()?.deletedAt) {
      // If status is not DELETED but tenant was previously deleted, remove deletedAt
      updateData.deletedAt = null;
    }

    await db.collection('TENANTS').doc(teamId).update(updateData);

    res.status(200).json({
      status: 'success',
      message: `Tenant status updated to ${status}`,
      data: {
        teamId,
        status,
        updatedAt: now,
      },
    });
  } catch (error) {
    logger.error(`Error updating tenant status: ${error}`);
    next(error);
  }
});

export default router;
