/**
 * Tenant status enum
 */
export enum TenantStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  DELETED = 'deleted'
}

/**
 * Tenant interface
 */
export interface Tenant {
  id: string;
  tier: string;
  prompt: string;
  thresholds: {
    notionSync: number;
  };
  registeredEmails: string[];
  registeredDomains: string[];
  extraQuota: number;
  quota: number;
  companyName: string;
  contactEmail: string;
  contactName: string;
  slackConnected: boolean;
  notionConnected: boolean;
  status: TenantStatus;
  joinDate: Date;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}
