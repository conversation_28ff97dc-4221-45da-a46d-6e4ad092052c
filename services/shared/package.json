{"name": "@valurize/shared", "version": "1.0.0", "description": "Shared utilities for Valurize services", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "test": "jest"}, "dependencies": {"@google-cloud/firestore": "^6.6.1", "@google-cloud/secret-manager": "^4.2.2", "@slack/oauth": "^2.6.1", "@slack/web-api": "^6.8.1", "winston": "^3.8.2"}, "devDependencies": {"@types/jest": "^29.5.1", "@types/node": "^18.16.3", "jest": "^29.5.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.0", "typescript": "^5.0.4"}}