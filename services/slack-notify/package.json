{"name": "slack-notify", "version": "1.0.0", "description": "Slack notification service for Valurize", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@google-cloud/pubsub": "^3.7.1", "@google-cloud/secret-manager": "^4.2.2", "@google-cloud/storage": "^6.11.0", "@slack/web-api": "^6.8.1", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.9.0", "helmet": "^7.0.0", "winston": "^3.9.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.3", "jest": "^29.5.0", "nodemon": "^2.0.22", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}