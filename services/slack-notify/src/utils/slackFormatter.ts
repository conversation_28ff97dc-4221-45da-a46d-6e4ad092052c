/**
 * Formats an analysis result into a Slack message
 *
 * @param analysis The analysis result
 * @param batchId The batch ID
 * @returns Slack blocks for the message
 */
export function formatSlackMessage(analysis: any, batchId: string): any[] {
  // Create a color based on the score
  const scoreColor = getScoreColor(analysis.score);

  // Format strengths and weaknesses (limit to top 3 for better readability)
  const strengthsList = analysis.strengths.slice(0, 3).map((s: string) => `• ${s}`).join('\n');
  const weaknessesList = analysis.weaknesses.slice(0, 3).map((w: string) => `• ${w}`).join('\n');

  // Format key watchouts
  const watchoutsList = analysis.keyWatchouts && analysis.keyWatchouts.length > 0
    ? analysis.keyWatchouts.map((w: string) => `• ${w}`).join('\n')
    : "No significant watchouts identified.";

  // Format follow-up questions (limit to top 3)
  const questionsList = analysis.followUpQuestions.slice(0, 3).map((q: string) => `• ${q}`).join('\n');

  // Get recommendation emoji
  const recommendationEmoji = getRecommendationEmoji(analysis.investmentRecommendation);

  // Create blocks
  return [
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: `${analysis.companyName} Analysis Complete`,
        emoji: true,
      },
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Score: ${analysis.score}/100* ${getScoreEmoji(analysis.score)}  |  *Recommendation: ${analysis.investmentRecommendation}* ${recommendationEmoji}`,
      },
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*One-Line Description*\n${analysis.oneSentenceDescription || analysis.summary.split('.')[0]}`,
      },
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Summary*\n${analysis.summary}`,
      },
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Key Strengths*\n${strengthsList}`,
        },
        {
          type: 'mrkdwn',
          text: `*Key Weaknesses*\n${weaknessesList}`,
        },
      ],
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Key Watchouts*\n${watchoutsList}`,
      },
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Team Score*\n${analysis.teamScore}/5.0`,
        },
        {
          type: 'mrkdwn',
          text: `*Market Score*\n${analysis.marketScore}/5.0`,
        },
      ],
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Traction Score*\n${analysis.tractionScore}/5.0`,
        },
        {
          type: 'mrkdwn',
          text: `*Outlier Potential*\n${analysis.outlierPotentialScore}/5.0`,
        },
      ],
    },
    {
      type: 'divider',
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Follow-up Questions*\n${questionsList}`,
      },
    },
    // Add a button to view the detailed memo if available
    analysis.memoUrl ? {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '*Detailed Analysis*',
      },
      accessory: {
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'View Full Memo',
          emoji: true,
        },
        url: analysis.memoUrl,
        action_id: 'view_memo',
      },
    } : null,
    {
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `Analysis ID: ${batchId} | Generated: ${new Date().toLocaleString()}`,
        },
      ],
    },
  ].filter(Boolean); // Remove null blocks
}

/**
 * Gets a color based on the score
 *
 * @param score The score (0-100)
 * @returns A color hex code
 */
function getScoreColor(score: number): string {
  if (score >= 80) {
    return '#36a64f'; // Green
  } else if (score >= 60) {
    return '#ecb613'; // Yellow
  } else {
    return '#e01e5a'; // Red
  }
}

/**
 * Gets an emoji based on the score
 *
 * @param score The score (0-100)
 * @returns An emoji
 */
function getScoreEmoji(score: number): string {
  if (score >= 80) {
    return ':star::star::star:';
  } else if (score >= 60) {
    return ':star::star:';
  } else if (score >= 40) {
    return ':star:';
  } else {
    return ':x:';
  }
}

/**
 * Gets an emoji based on the investment recommendation
 *
 * @param recommendation The investment recommendation
 * @returns An emoji
 */
function getRecommendationEmoji(recommendation: string): string {
  const lowerRec = recommendation.toLowerCase();

  if (lowerRec.includes('high priority') || lowerRec.includes('invest')) {
    return ':white_check_mark:';
  } else if (lowerRec.includes('maybe') || lowerRec.includes('further review')) {
    return ':thinking_face:';
  } else if (lowerRec.includes('pass')) {
    return ':no_entry:';
  } else {
    return '';
  }
}
