import express from 'express';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { Storage } from '@google-cloud/storage';
import { PubSub } from '@google-cloud/pubsub';
import { WebClient } from '@slack/web-api';
import { logger } from './utils/logger';
import { formatSlackMessage } from './utils/slackFormatter';
import { db } from './utils/firebase';

// Load environment variables
dotenv.config();

// Initialize Secret Manager
const secretManager = new SecretManagerServiceClient();

// Initialize Storage
const storage = new Storage();
const resultsBucketName = process.env.RESULTS_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-results`;
const resultsBucket = storage.bucket(resultsBucketName);

// Create Express app
const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(helmet());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Manually send notification for a batch
app.post('/notify/:batchId', async (req, res) => {
  const { batchId } = req.params;

  try {
    // Check if batch exists
    const batchDoc = await db.collection('BATCHES').doc(batchId).get();

    if (!batchDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Batch not found',
      });
    }

    const batch = batchDoc.data();

    if (!batch) {
      return res.status(404).json({
        status: 'error',
        message: 'Batch data not found',
      });
    }

    // Check if batch is completed
    if (batch.status !== 'completed') {
      return res.status(400).json({
        status: 'error',
        message: 'Batch is not completed',
      });
    }

    // Send notification
    await sendSlackNotification(
      batch.teamId,
      batch.channelId,
      batchId,
      batch.companyName,
      batch.score,
      batch.resultPath
    );

    res.status(200).json({
      status: 'success',
      message: 'Notification sent',
    });
  } catch (error) {
    logger.error(`Error sending notification for batch ${batchId}: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to send notification',
    });
  }
});

// Set up PubSub subscription
const subscriptionName = process.env.ANALYSIS_COMPLETE_SUBSCRIPTION || 'analysis-complete-subscription';
const subscription = new PubSub().subscription(subscriptionName);

subscription.on('message', async (message) => {
  try {
    const data = JSON.parse(message.data.toString());
    logger.info(`Received message: ${JSON.stringify(data)}`);

    // Handle batch-level message format
    if (data.completedBatches && Array.isArray(data.completedBatches) && data.completedBatches.length > 0) {
      const { teamId, userId } = data;
      logger.info(`Processing ${data.completedBatches.length} completed batches for team ${teamId}`);

      // Process each completed batch
      for (const batch of data.completedBatches) {
        const { batchId, companyName, score, resultPath } = batch;

        // Get the batch from Firestore to get the channelId
        const batchDoc = await db.collection('BATCHES').doc(batchId).get();
        if (!batchDoc.exists) {
          logger.warn(`Batch ${batchId} not found in Firestore`);
          continue;
        }

        const batchData = batchDoc.data();
        if (!batchData) {
          logger.warn(`Batch data for ${batchId} not found`);
          continue;
        }

        const channelId = batchData.channelId;
        if (!channelId) {
          logger.warn(`No channelId found for batch ${batchId}`);
          continue;
        }

        logger.info(`Sending notification for batch ${batchId} to team ${teamId} in channel ${channelId}`);
        await sendSlackNotification(teamId, channelId, batchId, companyName, score, resultPath);
      }
    } else {
      logger.error(`Invalid message format: ${JSON.stringify(data)}`);
    }

    // Acknowledge the message
    message.ack();
  } catch (error) {
    logger.error(`Error processing message: ${error}`);
    message.nack();
  }
});

// Send notification to Slack
async function sendSlackNotification(
  teamId: string,
  channelId: string,
  batchId: string,
  companyName: string,
  score: number,
  resultPath: string
) {
  try {
    // Get Slack token for the team
    const slackToken = await getTeamSlackToken(teamId);

    if (!slackToken) {
      throw new Error(`No Slack token found for team ${teamId}`);
    }

    // Create Slack client
    const slack = new WebClient(slackToken);

    // Get analysis result from GCS
    const [fileContent] = await resultsBucket.file(resultPath).download();
    const analysis = JSON.parse(fileContent.toString());

    // Format message for Slack
    const blocks = formatSlackMessage(analysis, batchId);

    // Send message to Slack
    await slack.chat.postMessage({
      channel: channelId,
      text: `Analysis complete for ${companyName} (Score: ${score}/100)`,
      blocks,
    });

    logger.info(`Notification sent for batch ${batchId} to team ${teamId}`);
  } catch (error) {
    logger.error(`Error sending Slack notification: ${error}`);
    throw error;
  }
}

// Helper function to get Slack token for a team
async function getTeamSlackToken(teamId: string): Promise<string | null> {
  try {
    const secretName = `projects/${process.env.GOOGLE_CLOUD_PROJECT}/secrets/slack-token-${teamId}/versions/latest`;
    const [version] = await secretManager.accessSecretVersion({ name: secretName });

    if (!version.payload || !version.payload.data) {
      return null;
    }

    return version.payload.data.toString();
  } catch (error) {
    logger.error(`Error getting Slack token for team ${teamId}: ${error}`);
    return null;
  }
}

// Start server
app.listen(port, () => {
  logger.info(`Slack notification service listening on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});
