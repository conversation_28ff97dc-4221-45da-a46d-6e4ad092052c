#!/bin/bash
set -e

# Build the service
echo "Building slack-ingest service..."
npm run build

# Build the Docker image
echo "Building Docker image..."
IMAGE_NAME="slack-ingest"
PROJECT_ID=$(gcloud config get-value project)
REGION="us-central1"  # Change if your region is different
IMAGE_TAG="gcr.io/${PROJECT_ID}/${IMAGE_NAME}:latest"

docker build -t ${IMAGE_TAG} .

# Push the image to Google Container Registry
echo "Pushing image to GCR..."
docker push ${IMAGE_TAG}

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy ${IMAGE_NAME} \
  --image ${IMAGE_TAG} \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 50 \
  --min-instances 1 \
  --max-instances 100

echo "Deployment complete!"
