FROM node:18-alpine as builder

# Create app directory
WORKDIR /app

# Copy slack-ingest source first
COPY . /app/slack-ingest/
WORKDIR /app/slack-ingest

# Copy pre-built shared package
COPY shared/dist /app/shared/dist
COPY shared/package.json /app/shared/package.json

# Back to slack-ingest
WORKDIR /app/slack-ingest
RUN npm install

# Source code is already copied in the first COPY command

# Build slack-ingest
RUN npm run build

# Production image
FROM node:18-alpine

# Create app directory
WORKDIR /app

# Copy shared package
COPY --from=builder /app/shared/dist /app/shared/dist
COPY --from=builder /app/shared/package.json /app/shared/package.json

# Copy slack-ingest
WORKDIR /app/slack-ingest
COPY --from=builder /app/slack-ingest/package*.json ./
RUN npm install --only=production

# Copy built files
COPY --from=builder /app/slack-ingest/dist ./dist

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Expose port
EXPOSE 8080

# Start the service
CMD ["node", "dist/index.js"]
