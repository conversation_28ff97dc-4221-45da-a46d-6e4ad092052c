{"name": "slack-ingest", "version": "1.0.0", "description": "Slack integration for Valurize", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@google-cloud/pubsub": "^3.7.1", "@google-cloud/secret-manager": "^4.2.2", "@google-cloud/storage": "^6.11.0", "@slack/bolt": "^3.13.1", "@slack/oauth": "^2.6.3", "@slack/web-api": "^6.8.1", "@google-cloud/firestore": "^6.6.1", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.9.0", "helmet": "^7.0.0", "node-fetch": "^2.6.9", "uuid": "^9.0.0", "winston": "^3.9.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.3", "@types/node-fetch": "^2.6.3", "@types/uuid": "^9.0.2", "jest": "^29.5.0", "nodemon": "^2.0.22", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": [], "author": "", "license": "ISC"}