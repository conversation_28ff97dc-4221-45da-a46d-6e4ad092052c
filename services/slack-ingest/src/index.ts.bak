import { App, ExpressReceiver, LogLevel } from '@slack/bolt';
import { InstallProvider } from '@slack/oauth';
import express from 'express';
import helmet from 'helmet';
import dotenv from 'dotenv';
import path from 'path';
import { Storage } from '@google-cloud/storage';
import { PubSub } from '@google-cloud/pubsub';
import { v4 as uuidv4 } from 'uuid';
import { logger, SecretManagerInstallationStore, getSlackClient } from '@valurize/shared';
import { downloadFile } from './utils/fileDownloader';
import { processPendingBatches } from './batchProcessor';
import { db } from './utils/firebase';

// Load environment variables
dotenv.config();

// Initialize Storage
const storage = new Storage();
const bucketName = process.env.PDF_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-pdfs`;
const bucket = storage.bucket(bucketName);

// Initialize PubSub
const pubsub = new PubSub();
const pdfProcessingTopic = pubsub.topic(process.env.PDF_PROCESSING_TOPIC || 'pdf-processing');

// Create a custom installation store
const installationStore = new SecretManagerInstallationStore({
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
});

// Create an install provider
const installer = new InstallProvider({
  clientId: process.env.SLACK_CLIENT_ID || '',
  clientSecret: process.env.SLACK_CLIENT_SECRET || '',
  stateSecret: process.env.SLACK_STATE_SECRET || 'valurize-state-secret',
  installationStore,
  logLevel: LogLevel.INFO,
});

// Create Express receiver with the installer
const receiver = new ExpressReceiver({
  signingSecret: process.env.SLACK_SIGNING_SECRET || '',
  processBeforeResponse: true,
  installerOptions: {
    directInstall: true,
  },
});

// Create Slack app with the receiver
const app = new App({
  receiver,
  // No need to provide token here as it will be fetched from the installation store
  processBeforeResponse: true,
});
