import { App, ExpressReceiver, LogLevel } from '@slack/bolt';
import { InstallProvider } from '@slack/oauth';
import express from 'express';
import helmet from 'helmet';
import dotenv from 'dotenv';
import path from 'path';
import { Storage } from '@google-cloud/storage';
import { PubSub } from '@google-cloud/pubsub';
import { v4 as uuidv4 } from 'uuid';
import { logger, SecretManagerInstallationStore, getSlackClient } from './utils';
import { downloadFile } from './utils/fileDownloader';
import { processPendingBatches } from './batchProcessor';
import { db } from './utils/firebase';

// Load environment variables
dotenv.config();

// Initialize Storage
const storage = new Storage();
const bucketName = process.env.PDF_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-pdfs`;
const bucket = storage.bucket(bucketName);

// Initialize PubSub
const pubsub = new PubSub();
const pdfProcessingTopic = pubsub.topic(process.env.PDF_PROCESSING_TOPIC || 'pdf-processing');

// Create a custom installation store
const installationStore = new SecretManagerInstallationStore({
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
});

// Create an install provider
const installer = new InstallProvider({
  clientId: process.env.SLACK_CLIENT_ID || '',
  clientSecret: process.env.SLACK_CLIENT_SECRET || '',
  stateSecret: process.env.SLACK_STATE_SECRET || 'valurize-state-secret',
  installationStore,
  logLevel: LogLevel.INFO,
});

// Create Express receiver with the installer
const receiver = new ExpressReceiver({
  signingSecret: process.env.SLACK_SIGNING_SECRET || '',
  processBeforeResponse: true,
  installerOptions: {
    directInstall: true,
  },
});

// Create Slack app with the receiver
const app = new App({
  receiver,
  // Use the installation store to authorize requests
  authorize: async ({ teamId, enterpriseId }) => {
    try {
      // Fetch installation from the installation store
      const installation = await installationStore.fetchInstallation({
        teamId,
        enterpriseId,
        isEnterpriseInstall: false
      });

      // Return authorization info
      return {
        botToken: installation.bot?.token,
        botId: installation.bot?.id,
        botUserId: installation.bot?.userId
      };
    } catch (error) {
      logger.error(`Failed to authorize request for team ${teamId}:`, error);
      throw error;
    }
  },
  processBeforeResponse: true,
});

// Handle file_shared events
app.event('file_shared', async ({ event, context, client }) => {
  try {
    logger.info(`File shared event received: ${JSON.stringify(event)}`);

    // Get file info
    const fileInfo = await client.files.info({
      file: event.file_id
    });

    if (fileInfo.file?.mimetype === 'application/pdf') {
      // Send message to the channel
      await client.chat.postMessage({
        channel: event.channel_id || '',
        text: `I received your PDF file: ${fileInfo.file.name}. Processing will begin shortly.`
      });

      // Download the file
      const downloadUrl = fileInfo.file.url_private_download || '';
      const fileName = fileInfo.file.name || 'unnamed.pdf';
      const teamId = context.teamId || '';

      if (downloadUrl) {
        try {
          // Download file and save to GCS
          const localPath = await downloadFile(downloadUrl, {
            headers: {
              Authorization: `Bearer ${client.token}`
            }
          });

          // Upload to GCS
          const destination = `${teamId}/${fileName}`;
          await bucket.upload(localPath, {
            destination,
            metadata: {
              contentType: 'application/pdf',
              metadata: {
                teamId,
                channelId: event.channel_id || '',
                userId: event.user_id || '',
                fileName
              }
            }
          });

          logger.info(`PDF file uploaded to GCS: ${destination}`);

          // Publish message to processing topic
          const messageData = {
            teamId,
            channelId: event.channel_id || '',
            userId: event.user_id || '',
            fileName,
            gcsPath: destination,
            timestamp: Date.now()
          };

          await pdfProcessingTopic.publishMessage({
            data: Buffer.from(JSON.stringify(messageData))
          });

          logger.info(`Published message to processing topic for ${fileName}`);
        } catch (downloadError) {
          logger.error(`Error downloading/processing file: ${downloadError}`);
          await client.chat.postMessage({
            channel: event.channel_id || '',
            text: `Sorry, I had trouble processing your PDF file. Please try again later.`
          });
        }
      }
    }
  } catch (error) {
    logger.error(`Error processing file_shared event: ${error}`);
  }
});

// Handle /uploaddeck command
app.command('/uploaddeck', async ({ ack, respond }) => {
  await ack();
  await respond({
    text: "Please upload a PDF file to analyze. You can drag and drop a PDF file here or use the upload button."
  });
});

// Handle OAuth installation
receiver.router.get('/slack/install', (_req, res) => {
  const url = installer.generateInstallUrl({
    scopes: [
      'channels:history',
      'chat:write',
      'commands',
      'files:read',
      'incoming-webhook',
      'users:read'
    ],
    userScopes: [],
    redirectUri: `${process.env.SERVICE_URL}/slack/oauth_redirect`
  });

  // Convert Promise<string> to string
  url.then(urlString => {
    res.redirect(urlString);
  }).catch(error => {
    logger.error(`Error generating install URL: ${error}`);
    res.status(500).send('Error generating Slack installation URL');
  });
});

// Handle OAuth redirect
receiver.router.get('/slack/oauth_redirect', async (req, res) => {
  try {
    // Complete the installation
    await installer.handleCallback(req, res);
  } catch (error) {
    logger.error(`OAuth error: ${error}`);
    res.status(500).send(`OAuth Error: ${error}`);
  }
});

// Health check endpoint
receiver.router.get('/health', (_req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Start the app
(async () => {
  try {
    await app.start(process.env.PORT || 3000);
    logger.info(`⚡️ Slack Ingest app is running on port ${process.env.PORT || 3000}!`);
    logger.info(`Install URL: ${process.env.SERVICE_URL}/slack/install`);
  } catch (error) {
    logger.error(`Error starting app: ${error}`);
    process.exit(1);
  }
})();
