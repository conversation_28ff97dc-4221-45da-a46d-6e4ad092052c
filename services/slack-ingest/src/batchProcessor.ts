import { PubSub } from '@google-cloud/pubsub';
import { logger } from './utils/logger';
import { db } from './utils/firebase';

// Initialize PubSub
const pubsub = new PubSub();
const pdfProcessingTopic = pubsub.topic(process.env.PDF_PROCESSING_TOPIC || 'pdf-processing');

/**
 * Process pending user batches
 * This function is meant to be called by a Cloud Scheduler job
 */
export async function processPendingBatches() {
  try {
    // Get all pending batches
    const batchesSnapshot = await db.collection('BATCHES')
      .where('status', '==', 'pending')
      .get();

    if (batchesSnapshot.empty) {
      logger.info('No pending batches to process');
      return;
    }

    logger.info(`Found ${batchesSnapshot.size} pending batches to process`);

    // Group batches by team and user
    const batchesByUser = new Map<string, { teamId: string, userId: string, pdfList: string[] }>();

    batchesSnapshot.forEach(doc => {
      const batch = doc.data();
      const { teamId, userId } = batch;
      const key = `${teamId}_${userId || 'anonymous'}`;

      if (!batchesByUser.has(key)) {
        batchesByUser.set(key, { teamId, userId: userId || 'anonymous', pdfList: [] });
      }

      batchesByUser.get(key)?.pdfList.push(doc.id);
    });

    // Process each user's batch
    const promises = Array.from(batchesByUser.values()).map(async ({ teamId, userId, pdfList }) => {
      try {
        // Update batch status for all PDFs in this user's batch
        const updatePromises = pdfList.map(batchId =>
          db.collection('BATCHES').doc(batchId).update({
            status: 'processing',
            updatedAt: new Date(),
          })
        );

        await Promise.all(updatePromises);

        // Publish message to PDF processing topic
        await pdfProcessingTopic.publish(Buffer.from(JSON.stringify({
          teamId,
          userId,
          pdfList,
        })));

        logger.info(`Published batch for team ${teamId}, user ${userId} with ${pdfList.length} PDFs for processing`);
      } catch (error) {
        logger.error(`Error processing batch for team ${teamId}, user ${userId}: ${error}`);
      }
    });

    await Promise.all(promises);

    logger.info('Successfully processed all pending batches');
  } catch (error) {
    logger.error(`Error processing pending batches: ${error}`);
    throw error;
  }
}
