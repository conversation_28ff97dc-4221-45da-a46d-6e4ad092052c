import { initializeApp, cert, applicationDefault, getApps } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { logger } from './logger';

// Initialize Firebase Admin if not already initialized
if (getApps().length === 0) {
  try {
    // Initialize Firebase Admin
    // When running in Cloud Run, use applicationDefault credentials
    if (process.env.K_SERVICE) {
      // Running in Cloud Run
      initializeApp({
        projectId: process.env.GOOGLE_CLOUD_PROJECT || 'valurize-app',
      });
      logger.info('Initialized Firebase app with applicationDefault credentials in Cloud Run');
    } else if (process.env.NODE_ENV === 'production') {
      // Running in production but not in Cloud Run
      initializeApp({
        credential: applicationDefault(),
      });
      logger.info('Initialized Firebase app with applicationDefault credentials');
    } else {
      // Running in development
      initializeApp({
        credential: cert({
          projectId: process.env.GOOGLE_CLOUD_PROJECT || 'valurize-app',
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        }),
      });
      logger.info('Initialized Firebase app with service account credentials');
    }
  } catch (error) {
    logger.error(`Error initializing Firebase: ${error}`);
    throw error;
  }
} else {
  logger.info('Firebase app already initialized');
}

// Export Firestore instance
export const db = getFirestore();
