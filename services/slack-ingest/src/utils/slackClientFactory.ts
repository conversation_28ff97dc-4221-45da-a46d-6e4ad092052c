import { WebClient } from '@slack/web-api';
import { SecretManagerInstallationStore } from './secretManagerInstallationStore';
import { logger } from './logger';

// Create a shared installation store
const installationStore = new SecretManagerInstallationStore({
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
});

// Cache for WebClients to avoid creating new instances for the same team
const clientCache: Record<string, { client: WebClient, timestamp: number }> = {};
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Gets a Slack WebClient for a specific team
 *
 * @param teamId The team ID
 * @returns A WebClient instance authenticated for the team
 */
export async function getSlackClient(teamId: string): Promise<WebClient> {
  try {
    // Check cache first
    const now = Date.now();
    const cached = clientCache[teamId];
    if (cached && (now - cached.timestamp) < CACHE_TTL) {
      logger.debug(`Using cached Slack client for team ${teamId}`);
      return cached.client;
    }

    // Fetch installation from store
    logger.debug(`Creating new Slack client for team ${teamId}`);
    const installation = await installationStore.fetchInstallation({
      teamId,
      isEnterpriseInstall: false,
      enterpriseId: undefined
    }, logger);

    // Create new WebClient
    const client = new WebClient(installation.bot?.token || (installation as any).access_token);

    // Cache the client
    clientCache[teamId] = { client, timestamp: now };

    return client;
  } catch (error) {
    logger.error(`Error getting Slack client for team ${teamId}: ${error}`);
    throw new Error(`Failed to get Slack client for team ${teamId}`);
  }
}

/**
 * Clears the client cache for a specific team or all teams
 *
 * @param teamId Optional team ID to clear. If not provided, clears all cached clients.
 */
export function clearClientCache(teamId?: string): void {
  if (teamId) {
    delete clientCache[teamId];
    logger.debug(`Cleared cached Slack client for team ${teamId}`);
  } else {
    Object.keys(clientCache).forEach(key => delete clientCache[key]);
    logger.debug('Cleared all cached Slack clients');
  }
}
