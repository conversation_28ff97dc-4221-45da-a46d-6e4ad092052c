import axios from 'axios';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { logger } from './logger';

interface DownloadOptions {
  headers: {
    Authorization: string;
  };
}

/**
 * Downloads a file from a URL using the provided token for authentication
 *
 * @param url The URL to download the file from
 * @param options Options including authorization headers
 * @returns The path to the downloaded file
 */
export async function downloadFile(url: string, options: DownloadOptions): Promise<string> {
  try {
    const response = await axios.get(url, {
      headers: options.headers,
      responseType: 'arraybuffer',
    });

    // Create a temporary file
    const tempDir = os.tmpdir();
    const tempFilePath = path.join(tempDir, `${uuidv4()}.pdf`);

    // Write the file to disk
    fs.writeFileSync(tempFilePath, Buffer.from(response.data));

    logger.info(`File downloaded to ${tempFilePath}`);
    return tempFilePath;
  } catch (error) {
    logger.error(`Error downloading file from ${url}: ${error}`);
    throw new Error(`Failed to download file: ${error}`);
  }
}
