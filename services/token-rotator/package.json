{"name": "token-rotator", "version": "1.0.0", "description": "OAuth token rotation service for Valurize", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev src/index.ts", "test": "jest"}, "dependencies": {"@google-cloud/pubsub": "^3.7.0", "@google-cloud/secret-manager": "^4.2.2", "@google-cloud/firestore": "^6.6.1", "@notionhq/client": "^2.2.5", "@slack/oauth": "^2.6.1", "@slack/web-api": "^6.8.1", "dotenv": "^16.0.3", "express": "^4.18.2", "firebase-admin": "^11.8.0", "helmet": "^6.1.5", "node-fetch": "^2.6.9", "winston": "^3.8.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/node": "^18.16.3", "@types/node-fetch": "^2.6.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.0.4"}}