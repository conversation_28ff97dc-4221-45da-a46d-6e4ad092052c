# Valurize Shared Utilities

This package contains shared utilities for Valurize services, focusing on multi-tenant token management and Slack integration.

## Installation

```bash
npm install
npm run build
```

## Usage

### SecretManagerInstallationStore

A Slack InstallationStore implementation that uses Google Secret Manager for storing tokens and Firestore for tenant metadata.

```typescript
import { SecretManagerInstallationStore } from '@valurize/shared';

// Create a store instance
const installationStore = new SecretManagerInstallationStore({
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
});

// Use with Bolt's InstallProvider
const installer = new InstallProvider({
  clientId: process.env.SLACK_CLIENT_ID || '',
  clientSecret: process.env.SLACK_CLIENT_SECRET || '',
  stateSecret: process.env.SLACK_STATE_SECRET || 'valurize-state-secret',
  installationStore,
});
```

### Slack Client Factory

A utility to get authenticated WebClient instances for any service.

```typescript
import { getSlackClient } from '@valurize/shared';

// Get a client for a specific team
const client = await getSlackClient('T12345678');

// Use the client
await client.chat.postMessage({
  channel: 'C12345678',
  text: 'Hello, world!',
});

// Clear the client cache if needed
import { clearClientCache } from '@valurize/shared';
clearClientCache('T12345678'); // Clear for a specific team
clearClientCache(); // Clear all cached clients
```

### Logger

A shared logger implementation for consistent logging across services.

```typescript
import { logger } from '@valurize/shared';

logger.info('This is an info message');
logger.error('This is an error message');
logger.debug('This is a debug message');
```

## Environment Variables

- `GOOGLE_CLOUD_PROJECT`: The Google Cloud project ID
- `SLACK_CLIENT_ID`: The Slack client ID
- `SLACK_CLIENT_SECRET`: The Slack client secret
- `SLACK_STATE_SECRET`: A secret for Slack OAuth state verification
- `LOG_LEVEL`: The logging level (default: 'info')
