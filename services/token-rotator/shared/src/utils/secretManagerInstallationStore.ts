import { Installation, InstallationQuery, InstallationStore } from '@slack/oauth';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { Firestore } from '@google-cloud/firestore';
import { logger } from './logger';

/**
 * A Slack InstallationStore implementation that uses Google Secret Manager
 * for storing tokens and Firestore for tenant metadata.
 */
export class SecretManagerInstallationStore implements InstallationStore {
  private secretManager: SecretManagerServiceClient;
  private db: Firestore;
  private projectId: string;

  /**
   * Creates a new SecretManagerInstallationStore
   *
   * @param options Configuration options
   */
  constructor(options: { projectId?: string } = {}) {
    this.secretManager = new SecretManagerServiceClient();
    this.db = new Firestore();
    this.projectId = options.projectId || process.env.GOOGLE_CLOUD_PROJECT || '';

    if (!this.projectId) {
      throw new Error('Project ID is required. Set GOOGLE_CLOUD_PROJECT environment variable or pass projectId in options.');
    }
  }

  /**
   * Stores a Slack installation in Secret Manager and updates Firestore
   *
   * @param installation The Slack installation data
   * @param logger Optional logger
   */
  async storeInstallation(installation: Installation, logger?: any): Promise<void> {
    const { team, enterprise, isEnterpriseInstall, bot } = installation;
    const teamId = team?.id;

    if (!teamId && !isEnterpriseInstall) {
      throw new Error('Failed saving installation data: No team id or enterprise id');
    }

    const id = teamId || enterprise?.id;
    if (!id) {
      throw new Error('Failed saving installation data: No team id or enterprise id');
    }

    const secretId = `slack-token-${id}`;
    const secretName = `projects/${this.projectId}/secrets/${secretId}`;

    // Add issued_at timestamp to track token age
    const installationWithMeta = {
      ...installation,
      issued_at: Date.now()
    };

    try {
      // Check if secret exists
      try {
        await this.secretManager.getSecret({ name: secretName });
        logger?.debug(`Secret ${secretId} already exists`);
      } catch (error: any) {
        // Create secret if it doesn't exist
        if (error.code === 5) { // NOT_FOUND
          logger?.info(`Creating new secret: ${secretId}`);
          await this.secretManager.createSecret({
            parent: `projects/${this.projectId}`,
            secretId,
            secret: {
              replication: {
                automatic: {},
              },
            },
          });
        } else {
          throw error;
        }
      }

      // Add a new version of the secret
      logger?.debug(`Adding new version to secret: ${secretId}`);
      await this.secretManager.addSecretVersion({
        parent: secretName,
        payload: {
          data: Buffer.from(JSON.stringify(installationWithMeta)),
        },
      });

      // Update tenant record in Firestore
      logger?.info(`Updating tenant record for team: ${id}`);
      await this.db.collection('TENANTS').doc(id).set({
        slackConnected: true,
        slackBotUserId: bot?.userId,
        slackTeamName: team?.name,
        updatedAt: new Date(),
      }, { merge: true });

      logger?.info(`Successfully stored installation for team: ${id}`);
    } catch (error) {
      logger?.error(`Failed to store installation: ${error}`);
      throw error;
    }
  }

  /**
   * Fetches a Slack installation from Secret Manager
   *
   * @param query Query parameters to find the installation
   * @param logger Optional logger
   * @returns The installation data
   */
  async fetchInstallation(query: InstallationQuery<boolean>, logger?: any): Promise<Installation> {
    const teamId = query.teamId;
    const enterpriseId = query.enterpriseId;

    if (!teamId && !enterpriseId) {
      throw new Error('Failed fetching installation: No team id or enterprise id provided');
    }

    const id = teamId || enterpriseId;
    if (!id) {
      throw new Error('Failed fetching installation: No team id or enterprise id provided');
    }

    try {
      const secretName = `projects/${this.projectId}/secrets/slack-token-${id}/versions/latest`;
      logger?.debug(`Fetching installation from: ${secretName}`);

      const [version] = await this.secretManager.accessSecretVersion({ name: secretName });

      if (!version.payload || !version.payload.data) {
        throw new Error(`No installation found for team: ${id}`);
      }

      const installation = JSON.parse(version.payload.data.toString());
      logger?.debug(`Successfully fetched installation for team: ${id}`);

      return installation;
    } catch (error) {
      logger?.error(`Failed to fetch installation: ${error}`);
      throw error;
    }
  }

  /**
   * Marks a Slack installation as disconnected in Firestore
   * Note: We don't actually delete the secret, just update the tenant record
   *
   * @param query Query parameters to find the installation
   * @param logger Optional logger
   */
  async deleteInstallation(query: InstallationQuery<boolean>, logger?: any): Promise<void> {
    const teamId = query.teamId;
    const enterpriseId = query.enterpriseId;

    if (!teamId && !enterpriseId) {
      throw new Error('Failed deleting installation: No team id or enterprise id provided');
    }

    const id = teamId || enterpriseId;
    if (!id) {
      throw new Error('Failed deleting installation: No team id or enterprise id provided');
    }

    try {
      logger?.info(`Marking installation as disconnected for team: ${id}`);
      await this.db.collection('TENANTS').doc(id).update({
        slackConnected: false,
        updatedAt: new Date(),
      });

      logger?.info(`Successfully marked installation as disconnected for team: ${id}`);
    } catch (error) {
      logger?.error(`Failed to delete installation: ${error}`);
      throw error;
    }
  }
}
