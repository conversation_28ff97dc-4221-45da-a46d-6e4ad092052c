FROM node:18-alpine as builder

# Create app directory
WORKDIR /app

# Copy token-rotator source first
COPY . /app/token-rotator/
WORKDIR /app/token-rotator

# Copy pre-built shared package
COPY shared/dist /app/shared/dist
COPY shared/package.json /app/shared/package.json

# Back to token-rotator
WORKDIR /app/token-rotator
RUN npm install

# Source code is already copied in the first COPY command

# Build token-rotator
RUN npm run build

# Production image
FROM node:18-alpine

# Create app directory
WORKDIR /app

# Copy shared package
COPY --from=builder /app/shared/dist /app/shared/dist
COPY --from=builder /app/shared/package.json /app/shared/package.json

# Copy token-rotator
WORKDIR /app/token-rotator
COPY --from=builder /app/token-rotator/package*.json ./
RUN npm install --only=production

# Copy built files
COPY --from=builder /app/token-rotator/dist ./dist

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Expose port
EXPOSE 8080

# Start the service
CMD ["node", "dist/index.js"]
