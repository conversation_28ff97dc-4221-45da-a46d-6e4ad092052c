FROM node:18-alpine as builder

# Create app directory
WORKDIR /app

# Copy package.json files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY tsconfig.json ./
COPY src ./src

# Build the application
RUN npm run build

# Production image
FROM node:18-alpine

# Create app directory
WORKDIR /app

# Copy package.json files
COPY package*.json ./

# Install production dependencies
RUN npm install --only=production

# Copy built files
COPY --from=builder /app/dist ./dist

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Expose port
EXPOSE 8080

# Start the service
CMD ["node", "dist/index.js"]
