import { WebClient } from '@slack/web-api';
import fetch from 'node-fetch';
import { logger, SecretManagerInstallationStore } from '../utils';

// Create a shared installation store
const installationStore = new SecretManagerInstallationStore({
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
});

/**
 * Rotates a Slack token for a team
 *
 * @param teamId The team ID
 * @returns True if successful, false otherwise
 */
export async function rotateSlackToken(teamId: string): Promise<boolean> {
  try {
    logger.info(`Rotating Slack token for team ${teamId}`);

    // Get current installation from the store
    let installation;
    try {
      installation = await installationStore.fetchInstallation({
        teamId,
        isEnterpriseInstall: false,
        enterpriseId: undefined
      });
    } catch (error) {
      logger.warn(`No Slack installation found for team ${teamId}`);
      return false;
    }

    // Check if we have a refresh token
    if (!(installation as any).refresh_token) {
      logger.warn(`No refresh token available for team ${teamId}`);
      return false;
    }

    // Check if token needs rotation
    const expiresAt = (installation as any).issued_at + (((installation as any).expires_in || 43200) * 1000);
    const now = Date.now();
    const timeUntilExpiration = expiresAt - now;

    // Define check interval (11 hours in milliseconds) to match our Cloud Scheduler interval
    const CHECK_INTERVAL_HOURS = 11;
    const CHECK_INTERVAL_MS = CHECK_INTERVAL_HOURS * 60 * 60 * 1000;

    // If token expires in more time than our check interval, no need to rotate yet
    if (timeUntilExpiration > CHECK_INTERVAL_MS) {
      logger.info(`Slack token for team ${teamId} is still valid (expires in ${Math.floor(timeUntilExpiration / 1000 / 60)} minutes), check again later`);
      return true;
    }

    // If we reach here, the token expires sometime before the next scheduled check runs
    logger.info(`Slack token for team ${teamId} expires within the next ${CHECK_INTERVAL_HOURS} hours (in ${Math.floor(timeUntilExpiration / 1000 / 60)} minutes), rotating now`);

    // Rotate token using refresh token
    try {
      const response = await fetch('https://slack.com/api/oauth.v2.access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: process.env.SLACK_CLIENT_ID || '',
          client_secret: process.env.SLACK_CLIENT_SECRET || '',
          grant_type: 'refresh_token',
          refresh_token: (installation as any).refresh_token,
        }).toString(),
      });

      if (!response.ok) {
        logger.error(`Error refreshing Slack token for team ${teamId}: ${await response.text()}`);
        return false;
      }

      const newTokenData = await response.json();

      // Update the installation with new token data
      const updatedInstallation = {
        ...installation,
        ...newTokenData,
        issued_at: Date.now(),
      };

      // Store the updated installation
      await installationStore.storeInstallation(updatedInstallation);

      logger.info(`Successfully rotated Slack token for team ${teamId}`);
      return true;
    } catch (error) {
      logger.error(`Error refreshing Slack token for team ${teamId}: ${error}`);

      // If refresh token is invalid, we need manual reauthorization
      logger.warn(`Slack token for team ${teamId} needs manual reauthorization`);

      // In a real implementation, you might want to:
      // 1. Send notification to team admins
      // 2. Update a status in Firestore
      // 3. Trigger a workflow to handle reauthorization

      return false;
    }
  } catch (error) {
    logger.error(`Error rotating Slack token for team ${teamId}: ${error}`);
    return false;
  }
}

/**
 * Gets a Slack token for a team
 *
 * @param teamId The team ID
 * @returns The Slack token or null if not found
 */
export async function getSlackToken(teamId: string): Promise<string | null> {
  try {
    const installation = await installationStore.fetchInstallation({
      teamId,
      isEnterpriseInstall: false,
      enterpriseId: undefined
    });
    return installation.bot?.token || (installation as any).access_token;
  } catch (error) {
    logger.error(`Error getting Slack token for team ${teamId}: ${error}`);
    return null;
  }
}
