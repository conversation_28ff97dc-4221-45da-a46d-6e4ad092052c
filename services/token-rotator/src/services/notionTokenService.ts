import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import fetch from 'node-fetch';
import { logger } from '../utils';

// Initialize Secret Manager
const secretManager = new SecretManagerServiceClient();

/**
 * Notion token data interface
 */
interface NotionTokenData {
  access_token: string;
  bot_id: string;
  duplicated_template_id?: string;
  owner?: {
    user?: {
      id: string;
      name: string;
      avatar_url: string;
    };
    workspace?: {
      id: string;
      name: string;
      icon: string;
    };
  };
  workspace_icon?: string;
  workspace_id: string;
  workspace_name: string;
  token_type: string;
  refresh_token?: string;
  expires_at?: number;
}

/**
 * Rotates a Notion token for a team
 *
 * @param teamId The team ID
 * @returns True if successful, false otherwise
 */
export async function rotateNotionToken(teamId: string): Promise<boolean> {
  try {
    logger.info(`Rotating Notion token for team ${teamId}`);

    // Get current token data from Secret Manager
    const tokenData = await getNotionToken(teamId);

    if (!tokenData) {
      logger.warn(`No Notion token found for team ${teamId}`);
      return false;
    }

    // Check if token needs rotation
    const now = Math.floor(Date.now() / 1000);

    // If token doesn't have expiration or refresh token, it's a legacy token that doesn't expire
    if (!tokenData.expires_at || !tokenData.refresh_token) {
      logger.info(`Notion token for team ${teamId} doesn't expire`);
      return true;
    }

    // If token is not close to expiration, no need to rotate
    // Refresh when we're within 24 hours of expiration
    if (tokenData.expires_at > now + 86400) {
      logger.info(`Notion token for team ${teamId} is still valid`);
      return true;
    }

    logger.info(`Notion token for team ${teamId} needs rotation`);

    // Refresh the token
    const response = await fetch('https://api.notion.com/v1/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${Buffer.from(`${process.env.NOTION_CLIENT_ID}:${process.env.NOTION_CLIENT_SECRET}`).toString('base64')}`,
      },
      body: JSON.stringify({
        grant_type: 'refresh_token',
        refresh_token: tokenData.refresh_token,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`Error refreshing Notion token for team ${teamId}: ${errorText}`);
      return false;
    }

    const newTokenData = await response.json() as NotionTokenData;

    // Store the new token in Secret Manager
    await storeNotionToken(teamId, newTokenData);

    logger.info(`Successfully rotated Notion token for team ${teamId}`);
    return true;
  } catch (error) {
    logger.error(`Error rotating Notion token for team ${teamId}: ${error}`);
    return false;
  }
}

/**
 * Gets a Notion token for a team from Secret Manager
 *
 * @param teamId The team ID
 * @returns The Notion token data or null if not found
 */
export async function getNotionToken(teamId: string): Promise<NotionTokenData | null> {
  try {
    const secretName = `projects/${process.env.GOOGLE_CLOUD_PROJECT}/secrets/notion-token-${teamId}/versions/latest`;
    const [version] = await secretManager.accessSecretVersion({ name: secretName });

    if (!version.payload || !version.payload.data) {
      return null;
    }

    return JSON.parse(version.payload.data.toString()) as NotionTokenData;
  } catch (error) {
    logger.error(`Error getting Notion token for team ${teamId}: ${error}`);
    return null;
  }
}

/**
 * Stores a Notion token for a team in Secret Manager
 *
 * @param teamId The team ID
 * @param tokenData The token data to store
 */
async function storeNotionToken(teamId: string, tokenData: NotionTokenData): Promise<void> {
  try {
    const secretName = `projects/${process.env.GOOGLE_CLOUD_PROJECT}/secrets/notion-token-${teamId}`;

    // Add a new version of the secret
    await secretManager.addSecretVersion({
      parent: secretName,
      payload: {
        data: Buffer.from(JSON.stringify(tokenData)),
      },
    });

    logger.info(`Stored new Notion token for team ${teamId}`);
  } catch (error) {
    logger.error(`Error storing Notion token for team ${teamId}: ${error}`);
    throw error;
  }
}
