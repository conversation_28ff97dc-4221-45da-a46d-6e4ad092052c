import express from 'express';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { initializeApp, cert, applicationDefault } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { PubSub } from '@google-cloud/pubsub';
import { logger } from './utils';
import { rotateSlackToken } from './services/slackTokenService';
import { rotateNotionToken } from './services/notionTokenService';

// Load environment variables
dotenv.config();

// Initialize Firebase Admin
try {
  // When running in Cloud Run, use applicationDefault credentials
  if (process.env.K_SERVICE) {
    // Running in Cloud Run
    initializeApp({
      projectId: process.env.GOOGLE_CLOUD_PROJECT || 'valurize-app',
    });
    logger.info('Initialized Firebase app with applicationDefault credentials in Cloud Run');
  } else if (process.env.NODE_ENV === 'production') {
    // Running in production but not in Cloud Run
    initializeApp({
      credential: applicationDefault(),
    });
    logger.info('Initialized Firebase app with applicationDefault credentials');
  } else {
    // Running in development
    initializeApp({
      credential: cert({
        projectId: process.env.GOOGLE_CLOUD_PROJECT || 'valurize-app',
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    logger.info('Initialized Firebase app with service account credentials');
  }
} catch (error) {
  logger.error(`Error initializing Firebase: ${error}`);
  throw error;
}

// Initialize Firestore
const db = getFirestore();

// Create Express app
const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(helmet());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Manual token rotation endpoint (for testing and admin use)
app.post('/rotate/:teamId', async (req, res) => {
  try {
    const { teamId } = req.params;
    const { service } = req.query;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    const tenant = tenantDoc.data();

    if (!tenant) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant data not found',
      });
    }

    let result = false;

    // Rotate tokens based on service parameter
    if (service === 'slack' && tenant.slackConnected) {
      result = await rotateSlackToken(teamId);
    } else if (service === 'notion' && tenant.notionConnected) {
      result = await rotateNotionToken(teamId);
    } else if (!service) {
      // Rotate all tokens
      const results = [];

      if (tenant.slackConnected) {
        results.push(await rotateSlackToken(teamId));
      }

      if (tenant.notionConnected) {
        results.push(await rotateNotionToken(teamId));
      }

      result = results.some(r => r);
    } else {
      return res.status(400).json({
        status: 'error',
        message: `Invalid service or service not connected: ${service}`,
      });
    }

    if (result) {
      res.status(200).json({
        status: 'success',
        message: 'Token rotation successful',
      });
    } else {
      res.status(500).json({
        status: 'error',
        message: 'Token rotation failed',
      });
    }
  } catch (error) {
    logger.error(`Error handling manual token rotation: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
    });
  }
});

// Set up PubSub subscription
const subscriptionName = process.env.TOKEN_ROTATE_SUBSCRIPTION || 'token-rotate-subscription';
const subscription = new PubSub().subscription(subscriptionName);

subscription.on('message', async (message) => {
  try {
    logger.info('Received token rotation request');

    // Get all tenants
    const tenantsSnapshot = await db.collection('TENANTS').get();

    if (tenantsSnapshot.empty) {
      logger.info('No tenants found');
      message.ack();
      return;
    }

    logger.info(`Found ${tenantsSnapshot.size} tenants to process`);

    // Process each tenant
    const promises = tenantsSnapshot.docs.map(async (doc) => {
      const teamId = doc.id;
      const tenant = doc.data();

      // Rotate Slack token if connected
      if (tenant.slackConnected) {
        await rotateSlackToken(teamId);
      }

      // Rotate Notion token if connected
      if (tenant.notionConnected) {
        await rotateNotionToken(teamId);
      }
    });

    // Wait for all rotations to complete
    await Promise.allSettled(promises);

    logger.info('Token rotation completed');

    // Acknowledge the message
    message.ack();
  } catch (error) {
    logger.error(`Error processing token rotation: ${error}`);
    message.nack();
  }
});

// Start server
app.listen(port, () => {
  logger.info(`Token rotation service listening on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});
