/**
 * Test script for Google Search grounding with the Google GenAI SDK
 *
 * This script tests the Google Search grounding functionality using the Google GenAI SDK
 * with Vertex AI integration.
 *
 * Run with: npx ts-node test-genai-grounding.ts
 */

import { GoogleGenAI } from '@google/generative-ai';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, 'src', '.env') });

/**
 * Test the Google Search grounding functionality with the Google GenAI SDK
 */
async function testGoogleSearchGrounding() {
  try {
    console.log('Starting Google Search grounding test with Google GenAI SDK...');

    // Get project and region from environment variables
    const project = process.env.VERTEX_AI_PROJECT_ID || process.env.GOOGLE_CLOUD_PROJECT || 'valurize-app';
    const location = process.env.VERTEX_AI_REGION || 'us-central1';

    console.log(`Using project: ${project}, location: ${location}`);

    // Initialize the Google GenAI client with Vertex AI configuration
    const ai = new GoogleGenAI({
      vertexai: true,
      project: project,
      location: location
    });

    // Model name for Gemini 2.5 Pro Preview
    const modelName = 'gemini-2.5-pro-preview-05-06';

    // Create the system instruction
    const systemInstruction = {
      text: `You are a helpful AI assistant that provides accurate and up-to-date information.
      When answering questions, please:
      1. Use web search to find the most current information
      2. Cite your sources with links
      3. Be concise but thorough
      4. Focus on factual information`
    };

    // Create the tools array for Google Search grounding
    const tools = [
      {
        googleSearch: {}
      }
    ];

    // Set up generation config
    const generationConfig = {
      maxOutputTokens: 2048,
      temperature: 0.2,
      topP: 0.95,
      responseModalities: ["TEXT"],
      tools: tools,
      systemInstruction: {
        parts: [systemInstruction]
      },
    };

    // Test prompt that should trigger Google Search grounding
    const testPrompt = `
    What are the latest updates on relations between India and Pakistan as of May 9th, 2025?
    Please provide specific recent events, diplomatic exchanges, or tensions.
    Include dates and sources where possible.
    `;

    // Create the request
    const request = {
      model: modelName,
      contents: [
        {
          parts: [
            { text: testPrompt }
          ]
        }
      ],
      config: generationConfig,
    };

    console.log('Generating content with Google Search grounding...');

    // Generate content
    const response = await ai.models.generateContent(request);

    console.log('\nResponse:');
    console.log('=========');
    console.log(response.text);

    // Check for grounding metadata
    if (response.groundingMetadata) {
      console.log('\nGrounding Metadata:');
      console.log('===================');

      // Log search queries
      if (response.groundingMetadata.webSearchQueries) {
        console.log('Search Queries:');
        response.groundingMetadata.webSearchQueries.forEach((query, index) => {
          console.log(`  ${index + 1}. ${query}`);
        });
      }

      // Log grounding sources
      if (response.groundingMetadata.groundingSources) {
        console.log('\nSources:');
        response.groundingMetadata.groundingSources.forEach((source, index) => {
          console.log(`  ${index + 1}. ${source.title}: ${source.uri}`);
        });
      }
    } else {
      console.log('\nNo grounding metadata found in the response.');
      console.log('This could mean that grounding was not activated or there was an issue with the configuration.');
    }

  } catch (error) {
    console.error(`Error testing Google Search grounding: ${error}`);
  }
}

// Run the test
testGoogleSearchGrounding();
