import express from 'express';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { Storage } from '@google-cloud/storage';
import { PubSub } from '@google-cloud/pubsub';
import { logger } from './utils/logger';
import { analyzeDocument } from './services/geminiService';
import { db, FieldValue } from './utils/firebase';

// Load environment variables
dotenv.config();

// Initialize Storage
const storage = new Storage();
const pdfBucketName = process.env.PDF_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-pdfs`;
const resultsBucketName = process.env.RESULTS_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-results`;
const pdfBucket = storage.bucket(pdfBucketName);
const resultsBucket = storage.bucket(resultsBucketName);

// Initialize PubSub
const pubsub = new PubSub();
const analysisCompleteTopic = pubsub.topic(process.env.ANALYSIS_COMPLETE_TOPIC || 'analysis-complete');

// Create Express app
const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(helmet());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Process a batch manually
app.post('/process/:batchId', async (req, res) => {
  const { batchId } = req.params;

  try {
    // Check if batch exists
    const batchDoc = await db.collection('BATCHES').doc(batchId).get();

    if (!batchDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Batch not found',
      });
    }

    const batch = batchDoc.data();

    if (!batch) {
      return res.status(404).json({
        status: 'error',
        message: 'Batch data not found',
      });
    }

    // Check if batch is already processed
    if (batch.status === 'completed') {
      return res.status(400).json({
        status: 'error',
        message: 'Batch already processed',
      });
    }

    // Queue the batch for processing
    await processBatch(batch.teamId, batch.userId, [batchId]);

    res.status(200).json({
      status: 'success',
      message: 'Batch queued for processing',
    });
  } catch (error) {
    logger.error(`Error processing batch ${batchId}: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process batch',
    });
  }
});

// Set up PubSub subscription
const subscriptionName = process.env.PDF_PROCESSING_SUBSCRIPTION || 'pdf-processing-subscription';
const subscription = pubsub.subscription(subscriptionName);

subscription.on('message', async (message) => {
  try {
    const data = JSON.parse(message.data.toString());
    const { teamId, userId, pdfList } = data;

    logger.info(`Processing batch for team ${teamId}, user ${userId} with ${pdfList.length} PDFs`);

    await processBatch(teamId, userId, pdfList);

    // Acknowledge the message
    message.ack();
  } catch (error) {
    logger.error(`Error processing message: ${error}`);
    message.nack();
  }
});

// Process a batch of PDFs sequentially
async function processBatch(teamId: string, userId: string, pdfList: string[]) {
  try {
    // Get tenant information
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      throw new Error(`Tenant ${teamId} not found`);
    }

    const tenant = tenantDoc.data();

    if (!tenant) {
      throw new Error(`Tenant data for ${teamId} not found`);
    }

    // Get custom prompt if available
    const prompt = tenant.prompt || 'Analyze this pitch deck and provide a detailed assessment.';

    // Process each PDF in the list sequentially
    for (const batchId of pdfList) {
      try {
        // Update batch status to processing
        await db.collection('BATCHES').doc(batchId).update({
          status: 'processing',
          updatedAt: new Date(),
        });

        // Get batch information
        const batchDoc = await db.collection('BATCHES').doc(batchId).get();
        if (!batchDoc.exists) {
          logger.error(`Batch ${batchId} not found`);
          continue;
        }

        const batch = batchDoc.data();
        if (!batch) {
          logger.error(`Batch data for ${batchId} not found`);
          continue;
        }

        // Download PDF from GCS
        const [fileBuffer] = await pdfBucket.file(batch.gcsPath).download();

        // Analyze the document using Gemini
        const analysis = await analyzeDocument(fileBuffer, prompt);

        // Extract company name and score
        const companyName = analysis.companyName || 'Unnamed Company';
        const score = analysis.score || 0;

        // Save detailed memo to GCS
        const memoPath = `results/${teamId}/${batchId}-memo.md`;
        await resultsBucket.file(memoPath).save(analysis.detailedMemo, {
          metadata: {
            contentType: 'text/markdown',
          },
        });

        // Generate the memo URL
        const memoUrl = `https://storage.googleapis.com/${resultsBucketName}/${memoPath}`;

        // Add the memo URL to the analysis result
        analysis.memoUrl = memoUrl;

        // Save analysis to GCS
        const resultPath = `results/${teamId}/${batchId}.json`;
        await resultsBucket.file(resultPath).save(JSON.stringify(analysis, null, 2), {
          metadata: {
            contentType: 'application/json',
          },
        });

        // Update batch in Firestore
        const now = new Date();
        await db.collection('BATCHES').doc(batchId).update({
          status: 'completed',
          companyName,
          score,
          resultPath,
          memoPath,
          memoUrl,
          industrySector: analysis.industrySector,
          recommendation: score >= 4.0 ? "High Priority" :
                          score >= 3.0 ? "Maybe" : "Pass",
          completedAt: now,
          updatedAt: now,
        });

        // Update usage metrics
        const cycleKey = `${teamId}_${now.getFullYear()}_${now.getMonth() + 1}`;
        await db.collection('USAGE').doc(cycleKey).set({
          count: FieldValue.increment(1),
          lastUpdated: now,
        }, { merge: true });

        // Delete the original PDF if configured to do so
        if (process.env.DELETE_ORIGINAL_PDF === 'true') {
          await pdfBucket.file(batch.gcsPath).delete();
        }

        logger.info(`Successfully processed batch ${batchId} for team ${teamId}`);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`Error processing batch ${batchId}: ${errorMessage}`);

        // Update batch status to failed
        await db.collection('BATCHES').doc(batchId).update({
          status: 'failed',
          error: errorMessage,
          updatedAt: new Date(),
        });
      }
    }

    // Get all completed batches
    const completedBatches = [];
    for (const batchId of pdfList) {
      const batchDoc = await db.collection('BATCHES').doc(batchId).get();
      if (batchDoc.exists && batchDoc.data()?.status === 'completed') {
        const batchData = batchDoc.data();
        completedBatches.push({
          batchId,
          companyName: batchData?.companyName,
          score: batchData?.score,
          resultPath: batchData?.resultPath,
          memoPath: batchData?.memoPath,
          memoUrl: batchData?.memoUrl
        });
      }
    }

    // Publish completion message for the entire batch
    await analysisCompleteTopic.publish(Buffer.from(JSON.stringify({
      teamId,
      userId,
      pdfList,
      completedBatches
    })));

    logger.info(`Published analysis complete message for team ${teamId} with ${completedBatches.length} completed batches`);

    logger.info(`Successfully processed all PDFs for team ${teamId}, user ${userId}`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Error processing batch for team ${teamId}, user ${userId}: ${errorMessage}`);
    throw error;
  }
}

// Start server
app.listen(port, () => {
  logger.info(`PDF processor service listening on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});
