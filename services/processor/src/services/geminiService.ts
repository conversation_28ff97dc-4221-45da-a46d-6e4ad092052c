import { logger } from '../utils/logger';
import { GoogleGenAI } from '@google/genai';

// Define the grounding metadata interfaces
export interface GroundingChunk {
  web: {
    uri: string;
    title: string;
  };
}

export interface GroundingSupport {
  segment: {
    startIndex?: number;
    endIndex: number;
    text: string;
  };
  groundingChunkIndices: number[];
  confidenceScores: number[];
}

export interface GroundingMetadata {
  searchEntryPoint?: {
    renderedContent: string;
  };
  groundingChunks?: GroundingChunk[];
  groundingSupports?: GroundingSupport[];
  webSearchQueries?: string[];
  // Add retrievalMetadata to the interface
  retrievalMetadata?: {
    webDynamicRetrievalScore?: number;
  };
  // Allow for additional properties
  [key: string]: any;
}

// Define the analysis result interface
export interface AnalysisResult {
  companyName: string;
  score: number;
  summary: string;
  strengths: string[];
  weaknesses: string[];
  marketOpportunity: {
    size: string;
    growth: string;
    assessment: string;
    sources?: string[];
  };
  team: {
    assessment: string;
    experience: string;
    gaps: string;
    sources?: string[];
  };
  financials: {
    assessment: string;
    runway: string;
    projections: string;
    sources?: string[];
  };
  investmentRecommendation: string;
  followUpQuestions: string[];
  // Additional fields for Notion and Slack payloads
  detailedMemo: string;
  industrySector: string[];
  founderPriorExit: boolean;
  teamScore: number;
  marketScore: number;
  tractionScore: number;
  outlierPotentialScore: number;
  oneSentenceDescription: string;
  keyWatchouts: string[];
  memoUrl?: string; // Added during processing
  outlierPotential?: string;
  sources?: {
    general?: string[];
    industry?: string[];
    competitors?: string[];
  };
  notionData?: {
    startup_name: string;
    one_sentence_description: string;
    analysis_date: string;
    overall_score: number;
    recommendation: string;
    industry_sector: string[];
    founder_prior_exit: boolean;
    team_score: number;
    market_score: number;
    traction_score: number;
    outlier_potential_score: number;
    key_watchouts: string;
  };
  // Google Search grounding metadata
  groundingMetadata?: GroundingMetadata;
}

/**
 * Note: The Notion and Slack services will generate their own payloads from the analysis result.
 * This approach is more efficient as it avoids storing redundant data.
 */

/**
 * Analyzes a PDF document using Gemini 2.5 Pro on Vertex AI
 *
 * @param fileBuffer The PDF file buffer
 * @param prompt The custom prompt to use
 * @returns The analysis result
 */
export async function analyzeDocument(
  fileBuffer: Buffer,
  prompt: string
): Promise<AnalysisResult> {
  try {
    // Get project and region from environment variables
    const project = process.env.VERTEX_AI_PROJECT_ID || process.env.GOOGLE_CLOUD_PROJECT;
    const location = process.env.VERTEX_AI_REGION || 'us-central1';

    if (!project) {
      throw new Error('No project ID found in environment variables');
    }

    logger.info(`Using project: ${project}, location: ${location}`);

    // Check if Google Search grounding is enabled
    const enableGrounding = process.env.ENABLE_GOOGLE_SEARCH_GROUNDING === 'true';

    // Set environment variables for Vertex AI
    process.env.GOOGLE_CLOUD_PROJECT = project;
    process.env.GOOGLE_CLOUD_LOCATION = location;
    process.env.GOOGLE_GENAI_USE_VERTEXAI = "True";

    // Model name for Gemini 2.5 Pro Preview - use the exact version from the source of truth
    const modelName = 'gemini-2.5-pro-preview-05-06';

    // Note: System instructions are included in the prompt directly

    logger.info(`Using Gemini 2.5 Pro Preview model with default thinking enabled${enableGrounding ? ' and Google Search grounding' : ''}`);

    // PDF buffer will be converted to base64 later

    // Define a single comprehensive prompt with no redundancy
    const analysisPrompt = `
You are Valurize, an AI pitch deck analysis system embodying the critical thinking and analytical rigor of a seasoned Venture Capital partner. Your objective is to conduct a thorough and insightful evaluation of the provided pitch deck, assign scores based on your findings, calculate a weighted total score, and determine a recommendation.

**VC Firm Context & Focus:**
<firm_context>
${prompt}
</firm_context>

**Core Analysis Task:**
Critically analyze the entire pitch deck based on the criteria below. Leverage your internal reasoning capabilities to assess the realism of statements, gather context, and analyze the information present in the deck.

IMPORTANT:
- Cross-check all information in the pitch deck with publicly available sources
- Research team members' LinkedIn profiles, news archives, Crunchbase, and online presence
- Verify market size claims (TAM, SAM, SOM) with latest industry reports and credible sources
- Check for any recent news about the company or founders
- Use your thinking capabilities to thoroughly analyze this pitch deck
- Take your time to reason through each aspect of the business before providing your final assessment

**Evaluation Criteria & Scoring (Assign 1-5 for each):**
(1: Significant Weakness/Red Flag, 2: Weakness, 3: Average/Adequate, 4: Strength, 5: Exceptional Strength)

1. **Team (Weight: 25%):**
   - Evaluate the founding team's experience, domain expertise, and track record
   - Verify backgrounds using LinkedIn, news archives, Crunchbase
   - Assess relevant experience, domain expertise, completeness
   - Determine if any founder has a verified prior successful exit

2. **Market (Weight: 20%):**
   - Assess market size, growth potential, and competitive landscape
   - Verify claimed TAM, SAM, and SOM against independent market research
   - Evaluate market growth trends and dynamics
   - Identify the primary industry/sector

3. **Product (Weight: 20%):**
   - Evaluate product-market fit, innovation, and technical feasibility
   - Research similar products/services in the market
   - Assess technological feasibility and implementation challenges
   - Evaluate intellectual property position and defensibility

4. **Traction (Weight: 15%):**
   - Analyze current customers, revenue, and growth metrics
   - Substantiate claimed milestones using external validation
   - Differentiate meaningful progress from vanity metrics
   - Assess stage-appropriateness

5. **Business Model (Weight: 10%):**
   - Evaluate revenue model, unit economics, and scalability
   - Compare with industry benchmarks
   - Assess pricing strategy against competitors
   - Research customer acquisition costs and lifetime value benchmarks

6. **Financials (Weight: 10%):**
   - Assess financial projections, funding needs, and use of funds
   - Evaluate the realism of forecasts and underlying assumptions
   - Compare key metrics against industry benchmarks
   - Assess capital efficiency and runway implications

7. **Outlier Potential (Weight: 5%):**
   - Look for signals of a potential high-risk/high-reward outlier
   - Assess if targeting a non-obvious problem/market
   - Evaluate for highly unconventional approach
   - Check for evidence of intense early user love in a niche
   - Look for potential for market creation
   - Assess for exceptional founder vision/obsession that deviates from norms

**Weighted Score Calculation:**
Calculate the Total Score by summing the weighted scores (Score * Weight) for all criteria. The maximum possible score is 5.0.

**Recommendation Logic:**
Based on the calculated Total Score:
- High Priority (Invest): Total Score >= 4.0
- Maybe (Further Review): Total Score >= 3.0 and < 4.0
- Pass: Total Score < 3.0

**Output Format:**
1. **Detailed Memo:**
   - **Executive Summary:** Key strengths, critical weaknesses, outlier potential note.
   - **Scoring & Analysis:** For each criterion, state the score (1–5), rationale, and citations.
   - **Outlier Assessment:** Score and justification.
   - **Red Flag Summary:** 1–2 sentence watchouts.
   - **Key Diligence Questions:** Actionable questions for founders.
2. **Notion & Slack Payloads:** Populate JSON objects matching your existing \`notion_payload\` and \`slack_payload\` schemas.
3. **Final Confirmation:** End with \`Analysis processing complete.\`

**Behavioral Rules:**
- Use grounded web search calls when verifying claims.
- Leverage chain-of-thought for deep analysis.
- Be concise, specific, and avoid fluff.
- Always cite sources for external data.
- Delete any sensitive or PII from logs and outputs.

Provide your response in the following JSON format:
{
  "companyName": "Name of the company",
  "score": <Calculated Total Weighted Score,  >,
  "summary": "Brief executive summary of the company and opportunity",
  "strengths": ["Strength 1", "Strength 2", ...],
  "weaknesses": ["Weakness 1", "Weakness 2", ...],
  "marketOpportunity": {
    "size": "Market size assessment",
    "growth": "Market growth assessment",
    "assessment": "Overall market opportunity assessment",
    "sources": ["Source 1", "Source 2", ...]
  },
  "team": {
    "assessment": "Overall team assessment",
    "experience": "Team experience assessment",
    "gaps": "Identified team gaps",
    "sources": ["Source 1", "Source 2", ...]
  },
  "financials": {
    "assessment": "Overall financial assessment",
    "runway": "Runway assessment",
    "projections": "Projection realism assessment",
    "sources": ["Source 1", "Source 2", ...]
  },
  "outlierPotential": "Assessment of potential for high-risk/high-reward outlier success",
  "investmentRecommendation": "Clear recommendation based on the logic above (High Priority, Maybe, Pass)",
  "followUpQuestions": ["Question 1", "Question 2", ...],
  "sources": {
    "general": ["Source 1", "Source 2", ...],
    "industry": ["Source 1", "Source 2", ..."],
    "competitors": ["Source 1", "Source 2", ..."]
  },
  "detailedMemo": "Full detailed analysis memo with all sections",
  "industrySector": ["Primary Industry", "Secondary Industry"],
  "founderPriorExit": true/false,
  "teamScore": <Score out of 5.0>,
  "marketScore": <Score out of 5.0>,
  "tractionScore": <Score out of 5.0>,
  "outlierPotentialScore": <Score out of 5.0>,
  "oneSentenceDescription": "A single sentence description of the company",
  "keyWatchouts": ["Watchout 1", "Watchout 2", "Watchout 3"],
  "notionData": {
    "startup_name": "Name of the company",
    "one_sentence_description": "A single sentence description of the company",
    "analysis_date": "Current date",
    "overall_score": <Score out of 5.0>,
    "recommendation": "High Priority, Maybe, or Pass",
    "industry_sector": ["Primary Industry", "Secondary Industry"],
    "founder_prior_exit": true/false,
    "team_score": <Score out of 5.0>,
    "market_score": <Score out of 5.0>,
    "traction_score": <Score out of 5.0>,
    "outlier_potential_score": <Score out of 5.0>,
    "key_watchouts": "• Watchout 1\\n• Watchout 2\\n• Watchout 3"
  }
}
`;

    // We're using the Google GenAI SDK with Vertex AI integration for Google Search grounding
    logger.info('Generating content with thinking enabled and Google Search grounding...');

    // Initialize the Google GenAI client with Vertex AI configuration
    const ai = new GoogleGenAI({
      vertexai: true,
      project: project,
      location: location
    });

    // Convert the PDF buffer to base64
    const base64PdfString = fileBuffer.toString('base64');

    // Create the system instruction with the prompt
    const siText1 = {text: analysisPrompt};

    // Create the tools array for Google Search grounding if enabled
    // Following the source of truth, we use an empty array by default
    // Only add Google Search tool if explicitly enabled
    const tools = enableGrounding ? [
      {
        googleSearch: {}
      }
    ] : [];

    if (enableGrounding) {
      logger.info('Google Search grounding enabled for Gemini model');
    }

    // Set up generation config - using exact values from source of truth
    // Using 'as any' to bypass TypeScript type checking since we're following the source of truth exactly
    const generationConfig: any = {
      maxOutputTokens: 8192,
      temperature: 0.6,  // Updated to match source of truth
      topP: 0.66,        // Updated to match source of truth
      seed: 0,
      responseModalities: ["TEXT"],
      safetySettings: [
        {
          category: 'HARM_CATEGORY_HATE_SPEECH',
          threshold: 'OFF',
        },
        {
          category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
          threshold: 'OFF',
        },
        {
          category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
          threshold: 'OFF',
        },
        {
          category: 'HARM_CATEGORY_HARASSMENT',
          threshold: 'OFF',
        }
      ],
      tools: tools,
      systemInstruction: {
        parts: [siText1]
      },
    };

    // Create the request
    // Following the source of truth format but maintaining PDF input
    // Adding a text part as required by the API
    const req = {
      model: modelName,
      contents: [
        {
          role: "user",
          parts: [
            {
              text: "Please analyze this pitch deck:"
            },
            {
              inlineData: {
                mimeType: 'application/pdf',
                data: base64PdfString
              }
            }
          ]
        }
      ],
      config: generationConfig,
    };

    // Generate content
    logger.info(`Using model: ${modelName} with Google Search grounding`);

    // For PDF analysis, we need the complete response at once
    // The source of truth uses streaming, but we need the full response for JSON parsing
    // We'll use the non-streaming API for simplicity and reliability
    // Using 'as any' to bypass TypeScript type checking since we're following the source of truth exactly
    const response: any = await ai.models.generateContent(req);

    // Note: The source of truth uses streaming API:
    // const streamingResp = await ai.models.generateContentStream(req);
    // But for our use case, we need to collect and parse the complete JSON response

    // Extract the text from the response
    const responseText = response.text;

    if (!responseText) {
      throw new Error('No text found in Gemini response');
    }

    // Extract the JSON from the response
    // First try to find JSON between ```json and ``` markers
    let jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);

    if (!jsonMatch) {
      // If not found, try to find any JSON object
      jsonMatch = responseText.match(/({[\s\S]*})/);
    }

    if (!jsonMatch) {
      throw new Error('No JSON found in Gemini response');
    }

    // Clean up the JSON string to handle potential issues
    let jsonString = jsonMatch[1];

    // Try to parse the JSON, handling any errors
    let rawAnalysis: any;
    try {
      // First attempt: direct parsing
      rawAnalysis = JSON.parse(jsonString);
      logger.info('Successfully parsed JSON response');
    } catch (error: any) {
      logger.error(`Error parsing JSON: ${error.message}`);
      logger.info('Attempting to clean the JSON string...');

      // Second attempt: clean the JSON string
      try {
        // Fix common JSON issues
        const cleanedJson = jsonString
          .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
          .replace(/\\(?!["\\/bfnrt])/g, '\\\\') // Escape backslashes
          .replace(/\n/g, '\\n') // Handle newlines
          .replace(/\r/g, '\\r') // Handle carriage returns
          .replace(/\t/g, '\\t') // Handle tabs
          .replace(/([^\\])\\([^"\\/bfnrtu])/g, '$1\\\\$2') // Fix invalid escapes
          .replace(/([^\\])\\'/g, "$1\\\\'") // Fix single quotes
          .replace(/\t/g, '\\t'); // Handle tabs

        rawAnalysis = JSON.parse(cleanedJson);
        logger.info('Successfully parsed JSON after cleaning');
      } catch (cleanError: any) {
        logger.error(`Still unable to parse JSON after cleaning: ${cleanError.message}`);

        // Third attempt: Use a more lenient JSON parser
        try {
          // Use Function constructor as a last resort
          rawAnalysis = (new Function('return ' + jsonString))();
          logger.info('Successfully parsed JSON using Function constructor');
        } catch (lenientError: any) {
          logger.error(`Failed to parse JSON with lenient parser: ${lenientError.message}`);

          // Fourth attempt: Extract directly from the response text
          logger.info('Attempting to extract JSON properties directly from response text...');

          // Extract key fields using regex
          const companyNameMatch = responseText.match(/"companyName"\s*:\s*"([^"]+)"/);
          const scoreMatch = responseText.match(/"score"\s*:\s*(\d+(\.\d+)?)/);
          const summaryMatch = responseText.match(/"summary"\s*:\s*"([^"]+)"/);
          const recommendationMatch = responseText.match(/"investmentRecommendation"\s*:\s*"([^"]+)"/);

          if (companyNameMatch && scoreMatch) {
            logger.info('Successfully extracted basic properties from response text');

            // Extract arrays
            const extractArray = (text: string, fieldName: string): string[] => {
              const regex = new RegExp(`"${fieldName}"\\s*:\\s*\\[(.*?)\\]`, 's');
              const match = text.match(regex);
              if (!match) return [];

              // Simple extraction of quoted strings
              const items: string[] = [];
              const itemRegex = /"([^"]+)"/g;
              let itemMatch;

              while ((itemMatch = itemRegex.exec(match[1])) !== null) {
                items.push(itemMatch[1]);
              }

              return items;
            };

            // Extract nested objects
            const extractNestedObject = (text: string, objectName: string): Record<string, any> | null => {
              const regex = new RegExp(`"${objectName}"\\s*:\\s*{([^}]+)}`, 's');
              const match = text.match(regex);
              if (!match) return null;

              const result: Record<string, any> = {};

              // Extract simple key-value pairs
              const keyValueRegex = /"([^"]+)"\s*:\s*"([^"]+)"/g;
              let keyValueMatch;
              while ((keyValueMatch = keyValueRegex.exec(match[1])) !== null) {
                result[keyValueMatch[1]] = keyValueMatch[2];
              }

              // Extract numeric values
              const numericRegex = /"([^"]+)"\s*:\s*(\d+(\.\d+)?)/g;
              let numericMatch;
              while ((numericMatch = numericRegex.exec(match[1])) !== null) {
                result[numericMatch[1]] = parseFloat(numericMatch[2]);
              }

              // Extract boolean values
              const booleanRegex = /"([^"]+)"\s*:\s*(true|false)/g;
              let booleanMatch;
              while ((booleanMatch = booleanRegex.exec(match[1])) !== null) {
                result[booleanMatch[1]] = booleanMatch[2] === 'true';
              }

              // Extract arrays
              const arrayFieldRegex = /"([^"]+)"\s*:\s*\[/g;
              let arrayFieldMatch;
              while ((arrayFieldMatch = arrayFieldRegex.exec(match[1])) !== null) {
                const arrayField = arrayFieldMatch[1];
                result[arrayField] = extractArray(match[1], arrayField);
              }

              return result;
            };

            // Extract numeric scores
            const extractScore = (text: string, scoreName: string): number | null => {
              const regex = new RegExp(`"${scoreName}"\\s*:\\s*(\\d+(\\.\\d+)?)`);
              const match = text.match(regex);
              return match ? parseFloat(match[1]) : null;
            };

            // Create a comprehensive object with all extracted properties
            rawAnalysis = {
              companyName: companyNameMatch[1],
              score: parseFloat(scoreMatch[1]),
              summary: summaryMatch ? summaryMatch[1] : '',
              investmentRecommendation: recommendationMatch ? recommendationMatch[1] : '',
              strengths: extractArray(responseText, 'strengths'),
              weaknesses: extractArray(responseText, 'weaknesses'),
              followUpQuestions: extractArray(responseText, 'followUpQuestions'),
              industrySector: extractArray(responseText, 'industrySector'),
              keyWatchouts: extractArray(responseText, 'keyWatchouts'),

              // Extract nested objects
              marketOpportunity: extractNestedObject(responseText, 'marketOpportunity'),
              team: extractNestedObject(responseText, 'team'),
              financials: extractNestedObject(responseText, 'financials'),

              // Extract scores
              teamScore: extractScore(responseText, 'teamScore'),
              marketScore: extractScore(responseText, 'marketScore'),
              tractionScore: extractScore(responseText, 'tractionScore'),
              outlierPotentialScore: extractScore(responseText, 'outlierPotentialScore'),

              // Extract other fields
              outlierPotential: responseText.match(/"outlierPotential"\s*:\s*"([^"]+)"/) ?
                responseText.match(/"outlierPotential"\s*:\s*"([^"]+)"/)[1] : '',
              oneSentenceDescription: responseText.match(/"oneSentenceDescription"\s*:\s*"([^"]+)"/) ?
                responseText.match(/"oneSentenceDescription"\s*:\s*"([^"]+)"/)[1] : '',
              detailedMemo: responseText.match(/"detailedMemo"\s*:\s*"([^"]+)"/) ?
                responseText.match(/"detailedMemo"\s*:\s*"([^"]+)"/)[1] : '',
              founderPriorExit: responseText.includes('"founderPriorExit": true'),

              // Extract notionData
              notionData: extractNestedObject(responseText, 'notionData'),

              // Initialize sources property
              sources: {}
            };

            // Extract sources if present
            const sourcesMatch = responseText.match(/"sources"\s*:\s*{([\s\S]+?)(?=},\s*"detailedMemo"|},\s*$)/);
            if (sourcesMatch) {
              try {
                // Try to parse the sources section as JSON
                const sourcesJson = `{${sourcesMatch[1]}}`;
                const parsedSources = JSON.parse(sourcesJson.replace(/'/g, '"'));
                rawAnalysis.sources = parsedSources;
              } catch (e: any) {
                logger.error(`Error parsing sources JSON: ${e.message}`);

                // Extract sources using regex as fallback
                const extractSourceObjects = (text: string, categoryName: string): Array<{url: string, title: string}> => {
                  const categoryRegex = new RegExp(`"${categoryName}"\\s*:\\s*\\[(.*?)\\]`, 's');
                  const categoryMatch = text.match(categoryRegex);
                  if (!categoryMatch) return [];

                  const sourceObjects: Array<{url: string, title: string}> = [];
                  const urlRegex = /"url"\s*:\s*"([^"]+)"/g;
                  const titleRegex = /"title"\s*:\s*"([^"]+)"/g;

                  let urlMatch;
                  let titleMatch;
                  const urls: string[] = [];
                  const titles: string[] = [];

                  while ((urlMatch = urlRegex.exec(categoryMatch[1])) !== null) {
                    urls.push(urlMatch[1]);
                  }

                  while ((titleMatch = titleRegex.exec(categoryMatch[1])) !== null) {
                    titles.push(titleMatch[1]);
                  }

                  // Create source objects with matching url and title
                  for (let i = 0; i < Math.min(urls.length, titles.length); i++) {
                    sourceObjects.push({
                      url: urls[i],
                      title: titles[i]
                    });
                  }

                  return sourceObjects;
                };

                rawAnalysis.sources = {
                  general: extractSourceObjects(sourcesMatch[1], 'general'),
                  industry: extractSourceObjects(sourcesMatch[1], 'industry'),
                  competitors: extractSourceObjects(sourcesMatch[1], 'competitors')
                };
              }
            }

            logger.info(`Extracted ${rawAnalysis.strengths.length} strengths, ${rawAnalysis.weaknesses.length} weaknesses`);
            if (rawAnalysis.sources) {
              logger.info(`Extracted sources: ${Object.keys(rawAnalysis.sources).map(k => `${k}: ${rawAnalysis.sources[k].length}`).join(', ')}`);
            }
          } else {
            logger.error('Failed to extract basic properties from response text');
            throw new Error('Unable to parse JSON response from Gemini. Please check the prompt and try again.');
          }
        }
      }
    }

    // Extract grounding metadata if available
    let groundingMetadata: GroundingMetadata | undefined;

    // Using 'as any' to access properties that might not be in the type definitions
    // but are present in the actual response according to the source of truth
    if ((response as any).groundingMetadata) {
      logger.info('Grounding metadata found in response');

      // Create a safe copy of the grounding metadata
      const metadata = (response as any).groundingMetadata;

      groundingMetadata = {} as GroundingMetadata;

      // Safely copy groundingSources if available (new format in GenAI SDK)
      if (metadata.groundingSources && metadata.groundingSources.length > 0) {
        groundingMetadata.groundingChunks = metadata.groundingSources.map((source: any) => {
          if (source.uri) {
            return {
              web: {
                uri: source.uri || '',
                title: source.title || ''
              }
            };
          }
          return { web: { uri: '', title: '' } };
        });
      }

      // Safely copy webSearchQueries if available
      if (metadata.webSearchQueries && metadata.webSearchQueries.length > 0) {
        groundingMetadata.webSearchQueries = [...metadata.webSearchQueries];
        logger.info(`Grounding search queries: ${groundingMetadata.webSearchQueries.join(', ')}`);
      }
    }

    // Create a complete analysis result with all required fields
    // Use overall_score from notionData if available, otherwise use score
    const score = rawAnalysis.notionData?.overall_score !== undefined
      ? rawAnalysis.notionData.overall_score
      : rawAnalysis.score;

    const analysis: AnalysisResult = {
      companyName: rawAnalysis.companyName,
      score: score,
      summary: rawAnalysis.summary,
      strengths: rawAnalysis.strengths,
      weaknesses: rawAnalysis.weaknesses,
      marketOpportunity: rawAnalysis.marketOpportunity,
      team: rawAnalysis.team,
      financials: rawAnalysis.financials,
      investmentRecommendation: rawAnalysis.investmentRecommendation,
      followUpQuestions: rawAnalysis.followUpQuestions,
      detailedMemo: rawAnalysis.detailedMemo,
      industrySector: rawAnalysis.industrySector,
      founderPriorExit: rawAnalysis.founderPriorExit,
      teamScore: rawAnalysis.teamScore,
      marketScore: rawAnalysis.marketScore,
      tractionScore: rawAnalysis.tractionScore,
      outlierPotentialScore: rawAnalysis.outlierPotentialScore,
      oneSentenceDescription: rawAnalysis.oneSentenceDescription,
      keyWatchouts: rawAnalysis.keyWatchouts,
      outlierPotential: rawAnalysis.outlierPotential,
      sources: rawAnalysis.sources,
      notionData: rawAnalysis.notionData,
      // Include grounding metadata if available
      groundingMetadata
    };

    return analysis;
  } catch (error: any) {
    logger.error(`Error analyzing document with Gemini: ${error.message || error}`);
    // Instead of returning mock data, throw the error
    throw error;
  }
}
