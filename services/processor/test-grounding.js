// Simple test script to verify Google Search grounding functionality
const { VertexAI } = require('@google-cloud/vertexai');

async function testGrounding() {
  try {
    console.log('Starting Google Search grounding test...');

    // Set project and region directly
    const project = 'valurize-app';
    const location = 'us-central1';

    console.log(`Using hardcoded values for testing - project: ${project}, location: ${location}`);

    // Initialize the Vertex AI client
    const vertexAI = new VertexAI({
      project: project,
      location: location,
    });

    // Get the Gemini model with Google Search grounding enabled
    const generativeModel = vertexAI.getGenerativeModel({
      model: 'gemini-2.5-pro-preview-03-25',
      generationConfig: {
        temperature: 0.2,
        topP: 0.95,
        maxOutputTokens: 8192,
      },
      tools: [{
        googleSearch: {}
      }]
    });

    console.log('Using Gemini 2.5 Pro Preview model with Google Search grounding');

    // Test prompt about India and Pakistan relations
    const testPrompt = `
    What are the latest updates on relations between India and Pakistan?
    Please provide specific recent events, diplomatic exchanges, or tensions.
    Include dates and sources where possible.
    `;

    // Make the request to Gemini
    const request = {
      contents: [
        {
          role: 'user',
          parts: [
            { text: testPrompt }
          ]
        }
      ]
    };

    console.log('Generating content with Google Search grounding...');

    // Generate content
    const response = await generativeModel.generateContent(request);
    const result = response.response;

    // Check if we have a valid response
    if (!result.candidates || result.candidates.length === 0) {
      throw new Error('No candidates returned from Gemini');
    }

    const candidate = result.candidates[0];
    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
      throw new Error('No content parts found in Gemini response');
    }

    // Extract the text from the response
    const responseText = candidate.content.parts[0].text;

    if (!responseText) {
      throw new Error('No text found in Gemini response');
    }

    console.log('\nResponse:');
    console.log('=========');
    console.log(responseText);

    // Check for grounding metadata
    if (candidate.groundingMetadata) {
      console.log('\nGrounding Metadata:');
      console.log('===================');

      // Log search queries
      if (candidate.groundingMetadata.webSearchQueries) {
        console.log('Search Queries:');
        candidate.groundingMetadata.webSearchQueries.forEach((query, index) => {
          console.log(`  ${index + 1}. ${query}`);
        });
      }

      // Log grounding chunks (sources)
      if (candidate.groundingMetadata.groundingChunks) {
        console.log('\nSources:');
        candidate.groundingMetadata.groundingChunks.forEach((chunk, index) => {
          if (chunk.web) {
            console.log(`  ${index + 1}. ${chunk.web.title}: ${chunk.web.uri}`);
          }
        });
      }
    } else {
      console.log('\nNo grounding metadata found in the response.');
      console.log('This could mean that the dynamic threshold was not met or there was an issue with grounding.');
    }

  } catch (error) {
    console.error(`Error testing Google Search grounding: ${error}`);
  }
}

// Run the test
testGrounding();
