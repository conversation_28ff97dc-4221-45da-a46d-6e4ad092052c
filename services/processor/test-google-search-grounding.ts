import { VertexAI } from '@google-cloud/vertexai';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, 'src', '.env') });

/**
 * Test the Google Search grounding functionality with Gemini
 */
async function testGoogleSearchGrounding() {
  try {
    console.log('Starting Google Search grounding test...');
    
    // Get project and region from environment variables
    const project = process.env.VERTEX_AI_PROJECT_ID || process.env.GOOGLE_CLOUD_PROJECT;
    const location = process.env.VERTEX_AI_REGION || 'us-central1';

    if (!project) {
      throw new Error('No project ID found in environment variables');
    }

    console.log(`Using project: ${project}, location: ${location}`);

    // Initialize the Vertex AI client
    const vertexAI = new VertexAI({
      project: project,
      location: location,
    });

    // Create model params with Google Search grounding
    const modelParams: any = {
      model: 'gemini-2.5-pro-preview-03-25',
      generationConfig: {
        temperature: 0.2,
        topP: 0.95,
        maxOutputTokens: 8192,
      },
      tools: [{
        googleSearchRetrieval: {
          dynamicRetrievalConfig: {
            mode: 'MODE_DYNAMIC',
            dynamicThreshold: 0.3
          }
        }
      }]
    };

    const generativeModel = vertexAI.getGenerativeModel(modelParams);

    console.log('Using Gemini 2.5 Pro Preview model with Google Search grounding');

    // Test prompt that should trigger Google Search grounding
    const testPrompt = `
    What are the latest developments in venture capital funding for AI startups in 2024?
    Please provide specific examples and data points.
    `;

    // Make the request to Gemini
    const request = {
      contents: [
        {
          role: 'user',
          parts: [
            { text: testPrompt }
          ]
        }
      ]
    };

    console.log('Generating content with Google Search grounding...');
    
    // Generate content
    const response = await generativeModel.generateContent(request);
    const result = response.response;

    // Check if we have a valid response
    if (!result.candidates || result.candidates.length === 0) {
      throw new Error('No candidates returned from Gemini');
    }

    const candidate = result.candidates[0];
    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
      throw new Error('No content parts found in Gemini response');
    }

    // Extract the text from the response
    const responseText = candidate.content.parts[0].text;

    if (!responseText) {
      throw new Error('No text found in Gemini response');
    }

    console.log('\nResponse:');
    console.log('=========');
    console.log(responseText);
    
    // Check for grounding metadata
    if (candidate.groundingMetadata) {
      console.log('\nGrounding Metadata:');
      console.log('===================');
      
      // Log search queries
      if (candidate.groundingMetadata.webSearchQueries) {
        console.log('Search Queries:');
        candidate.groundingMetadata.webSearchQueries.forEach((query, index) => {
          console.log(`  ${index + 1}. ${query}`);
        });
      }
      
      // Log grounding chunks (sources)
      if (candidate.groundingMetadata.groundingChunks) {
        console.log('\nSources:');
        candidate.groundingMetadata.groundingChunks.forEach((chunk, index) => {
          if (chunk.web) {
            console.log(`  ${index + 1}. ${chunk.web.title}: ${chunk.web.uri}`);
          }
        });
      }
      
      // Log search entry point (Google Search Suggestions)
      if (candidate.groundingMetadata.searchEntryPoint) {
        console.log('\nSearch Entry Point Available: Yes');
        console.log('This contains the Google Search Suggestions HTML that should be displayed to users');
      }
    } else {
      console.log('\nNo grounding metadata found in the response.');
      console.log('This could mean that the dynamic threshold was not met or there was an issue with grounding.');
    }
    
  } catch (error) {
    console.error(`Error testing Google Search grounding: ${error}`);
  }
}

// Run the test
testGoogleSearchGrounding();
