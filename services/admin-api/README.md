# Valurize Admin API

The Admin API service provides endpoints for managing tenants, users, and analytics for the Valurize platform.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file based on `.env.example`:
```bash
cp src/.env.example .env
```

3. Fill in the required environment variables in the `.env` file.

## Development

```bash
npm run dev
```

## Build

```bash
npm run build
```

## Deployment

```bash
./deploy-valurize-admin-api.sh
```

## API Endpoints

### Authentication

- `POST /api/users/login` - Login with email and password
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID (admin only)
- `POST /api/users` - Create user (admin only)

### Tenant Management

- `GET /api/tenants?includeDeleted=true|false` - Get all tenants (excludes deleted tenants by default)
- `GET /api/tenants/:id` - Get tenant by ID
- `POST /api/tenants` - Create tenant (admin only)
- `PUT /api/tenants/:id` - Update tenant (admin only)
- `DELETE /api/tenants/:id?soft=true|false` - Delete tenant (admin only, soft delete by default)
- `POST /api/tenants/:id/status` - Update tenant status (admin only)
- `GET /api/tenants/:id/usage` - Get tenant usage
- `POST /api/tenants/:id/reset-usage` - Reset tenant usage (admin only)
- `POST /api/tenants/:id/add-credits` - Add usage credits (admin only)

### Tenant Onboarding

- `GET /api/tenants/:id/onboarding-status` - Get tenant onboarding status
- `GET /api/tenants/:id/slack-install-url` - Generate Slack installation URL (admin only)
- `GET /api/tenants/:id/notion-connect-url` - Generate Notion connection URL (admin only)

### Analytics

- `GET /api/analytics/usage` - Get overall usage statistics
- `GET /api/analytics/batches` - Get batch statistics
- `GET /api/analytics/tenants` - Get tenant statistics

## Environment Variables

- `PORT` - Port to run the server on (default: 8080)
- `NODE_ENV` - Environment (development, production)
- `LOG_LEVEL` - Logging level (default: info)
- `GOOGLE_CLOUD_PROJECT` - Google Cloud project ID
- `FIREBASE_CLIENT_EMAIL` - Firebase client email
- `FIREBASE_PRIVATE_KEY` - Firebase private key
- `JWT_SECRET` - Secret for JWT token generation
- `SLACK_INGEST_URL` - URL of the Slack ingest service
- `NOTION_SYNC_URL` - URL of the Notion sync service
- `NOTION_CLIENT_ID` - Notion client ID
