import { getFirestore } from 'firebase-admin/firestore';
import { logger } from './logger';
import { TenantStatus } from '../types/tenant';

// Initialize Firestore
const db = getFirestore();

/**
 * Interface for tenant onboarding status
 */
export interface TenantOnboardingStatus {
  id: string;
  created: boolean;
  slackConnected: boolean;
  notionConnected: boolean;
  tier: string;
  status: TenantStatus;
  joinDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Gets the onboarding status of a tenant
 *
 * @param tenantId The tenant ID
 * @returns The tenant onboarding status
 */
export async function getTenantOnboardingStatus(tenantId: string): Promise<TenantOnboardingStatus | null> {
  try {
    // Get tenant document
    const tenantDoc = await db.collection('TENANTS').doc(tenantId).get();

    if (!tenantDoc.exists) {
      return null;
    }

    const tenantData = tenantDoc.data();

    if (!tenantData) {
      return null;
    }

    // Determine onboarding status
    return {
      id: tenantId,
      created: true,
      slackConnected: !!tenantData.slackConnected,
      notionConnected: !!tenantData.notionConnected,
      tier: tenantData.tier || 'free',
      status: tenantData.status || TenantStatus.ACTIVE,
      joinDate: tenantData.joinDate?.toDate(),
      createdAt: tenantData.createdAt?.toDate(),
      updatedAt: tenantData.updatedAt?.toDate(),
    };
  } catch (error) {
    logger.error(`Error getting tenant onboarding status: ${error}`);
    throw error;
  }
}

/**
 * Gets the quota for a specific tier
 *
 * @param tier The tier name
 * @returns The quota for the tier
 */
export function getTierQuota(tier: string): number {
  switch (tier.toLowerCase()) {
    case 'free':
      return 10;
    case 'pro':
      return 50;
    case 'enterprise':
      return 200;
    case 'custom':
      return 1000; // Custom tier has a configurable quota, default to 1000
    default:
      return 10; // Default to free tier quota
  }
}

/**
 * Validates tenant data
 *
 * @param id Tenant ID
 * @param tier Tier
 * @param status Tenant status
 * @returns Error message or null if valid
 */
export function validateTenantData(id: string, tier: string, status?: string): string | null {
  if (!id) {
    return 'Tenant ID is required';
  }

  if (!tier) {
    return 'Tier is required';
  }

  const validTiers = ['free', 'pro', 'enterprise', 'custom'];
  if (!validTiers.includes(tier.toLowerCase())) {
    return `Invalid tier. Must be one of: ${validTiers.join(', ')}`;
  }

  if (status) {
    const validStatuses = Object.values(TenantStatus);
    if (!validStatuses.includes(status as TenantStatus)) {
      return `Invalid status. Must be one of: ${validStatuses.join(', ')}`;
    }
  }

  return null;
}
