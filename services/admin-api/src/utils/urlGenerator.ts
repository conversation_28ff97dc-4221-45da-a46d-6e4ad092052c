import { logger } from './logger';

/**
 * Generates a Slack installation URL for a specific tenant
 * 
 * @param tenantId The tenant ID to use as state
 * @returns The Slack installation URL
 */
export function generateSlackInstallUrl(tenantId: string): string {
  try {
    // Get the Slack ingest service URL from environment variables
    const slackIngestUrl = process.env.SLACK_INGEST_URL;
    
    if (!slackIngestUrl) {
      throw new Error('SLACK_INGEST_URL environment variable is not set');
    }
    
    // Generate the installation URL with tenant ID as state
    return `${slackIngestUrl}/slack/install?state=${encodeURIComponent(tenantId)}`;
  } catch (error) {
    logger.error(`Error generating Slack install URL: ${error}`);
    throw error;
  }
}

/**
 * Generates a Notion connection URL for a specific tenant
 * 
 * @param tenantId The tenant ID to use as state
 * @returns The Notion connection URL
 */
export function generateNotionConnectUrl(tenantId: string): string {
  try {
    // Get required environment variables
    const notionClientId = process.env.NOTION_CLIENT_ID;
    const notionSyncUrl = process.env.NOTION_SYNC_URL;
    
    if (!notionClientId) {
      throw new Error('NOTION_CLIENT_ID environment variable is not set');
    }
    
    if (!notionSyncUrl) {
      throw new Error('NOTION_SYNC_URL environment variable is not set');
    }
    
    // Generate the redirect URI
    const redirectUri = `${notionSyncUrl}/notion/callback`;
    
    // Generate the Notion OAuth URL with tenant ID as state
    return `https://api.notion.com/v1/oauth/authorize?client_id=${encodeURIComponent(notionClientId)}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&state=${encodeURIComponent(tenantId)}`;
  } catch (error) {
    logger.error(`Error generating Notion connect URL: ${error}`);
    throw error;
  }
}
