import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import dotenv from 'dotenv';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import tenantRoutes from './routes/tenant';
import userRoutes from './routes/user';
import analyticsRoutes from './routes/analytics';

// Initialize Firebase first
import './utils/firebase';
import { db } from './utils/firebase';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(helmet());
// Configure CORS to allow requests from any origin
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Routes
app.use('/api/tenants', tenantRoutes);
app.use('/api/users', userRoutes);
app.use('/api/analytics', analyticsRoutes);

// Error handler
app.use(errorHandler);

// Create default admin user if it doesn't exist
async function createDefaultAdminUser() {
  try {
    const adminUsersSnapshot = await db.collection('ADMIN_USERS')
      .where('role', '==', 'admin')
      .limit(1)
      .get();

    if (adminUsersSnapshot.empty) {
      const now = new Date();

      await db.collection('ADMIN_USERS').add({
        email: '<EMAIL>',
        password: 'admin123', // In a real application, you would hash this password
        role: 'admin',
        createdAt: now,
        updatedAt: now,
      });

      logger.info('Created default admin user');
    }
  } catch (error) {
    logger.error(`Error creating default admin user: ${error}`);
  }
}

// Start the server
app.listen(port, async () => {
  logger.info(`Admin API service running on port ${port}`);

  // Create default admin user
  await createDefaultAdminUser();
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});
