import { Router } from 'express';
import { getFirestore } from 'firebase-admin/firestore';
import { authenticateJWT } from '../middleware/auth';
import { logger } from '../utils/logger';
import { TenantStatus } from '../types/tenant';

// Define types for batch data
interface BatchData {
  id: string;
  teamId: string;
  userId?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  companyName?: string;
  score?: number;
  recommendation?: 'High Priority' | 'Maybe' | 'Pass';
  resultPath?: string;
  memoPath?: string;
  memoUrl?: string;
  industrySector?: string;
  createdAt: Date | { toDate(): Date };
  updatedAt?: Date | { toDate(): Date };
  completedAt?: Date | { toDate(): Date };
  error?: string;
  gcsPath?: string;
  [key: string]: any;
}

// Helper function to convert Firestore timestamp to Date
const toDate = (timestamp: Date | { toDate(): Date }): Date => {
  if (timestamp instanceof Date) {
    return timestamp;
  }
  return timestamp.toDate();
};

// Initialize Firestore
const db = getFirestore();

// Create router
const router = Router();

// Helper function to get month name
const getMonthName = (month: number): string => {
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  return monthNames[month - 1];
};

// Get overall usage statistics
router.get('/usage', authenticateJWT, async (_req, res) => {
  try {
    // Get current month and year
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;

    // Get usage for all tenants for current month
    const usageSnapshot = await db.collection('USAGE')
      .where('lastUpdated', '>=', new Date(year, month - 1, 1))
      .where('lastUpdated', '<', new Date(year, month, 1))
      .get();

    const usageByTenant: Record<string, number> = {};
    let totalUsage = 0;

    usageSnapshot.forEach(doc => {
      const data = doc.data();
      const tenantId = doc.id.split('_')[0];

      usageByTenant[tenantId] = data.count;
      totalUsage += data.count;
    });

    res.status(200).json({
      status: 'success',
      data: {
        totalUsage,
        usageByTenant,
        month,
        year,
      },
    });
  } catch (error) {
    logger.error(`Error getting usage statistics: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get usage statistics',
    });
  }
});

// Get batch statistics
router.get('/batches', authenticateJWT, async (_req, res) => {
  try {
    // Get counts by status
    const pendingSnapshot = await db.collection('BATCHES')
      .where('status', '==', 'pending')
      .get();
    const pendingCount = pendingSnapshot.size;

    const processingSnapshot = await db.collection('BATCHES')
      .where('status', '==', 'processing')
      .get();
    const processingCount = processingSnapshot.size;

    const completedSnapshot = await db.collection('BATCHES')
      .where('status', '==', 'completed')
      .get();
    const completedCount = completedSnapshot.size;

    const failedSnapshot = await db.collection('BATCHES')
      .where('status', '==', 'failed')
      .get();
    const failedCount = failedSnapshot.size;

    // Get recent batches
    const recentBatchesSnapshot = await db.collection('BATCHES')
      .orderBy('createdAt', 'desc')
      .limit(10)
      .get();

    const recentBatches = recentBatchesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    res.status(200).json({
      status: 'success',
      data: {
        counts: {
          pending: pendingCount,
          processing: processingCount,
          completed: completedCount,
          failed: failedCount,
          total: pendingCount + processingCount + completedCount + failedCount,
        },
        recentBatches,
      },
    });
  } catch (error) {
    logger.error(`Error getting batch statistics: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get batch statistics',
    });
  }
});

// Get tenant statistics
router.get('/tenants', authenticateJWT, async (req, res) => {
  try {
    const { includeDeleted = 'false' } = req.query;
    const showDeleted = includeDeleted === 'true';

    // Get active tenant counts
    let query: FirebaseFirestore.Query<FirebaseFirestore.DocumentData> = db.collection('TENANTS');
    if (!showDeleted) {
      query = query.where('status', '!=', TenantStatus.DELETED);
    }
    const activeTenantsSnapshot = await query.get();
    const activeTenantCount = activeTenantsSnapshot.size;

    // Get tenants by tier and status
    const tierCounts: Record<string, number> = {};
    const statusCounts: Record<string, number> = {
      [TenantStatus.ACTIVE]: 0,
      [TenantStatus.PAUSED]: 0,
      [TenantStatus.DELETED]: 0,
    };

    const allTenantsSnapshot = await db.collection('TENANTS').get();

    allTenantsSnapshot.forEach(doc => {
      const data = doc.data();
      const tier = data.tier || 'unknown';
      const status = data.status || TenantStatus.ACTIVE;

      // Count by tier (excluding deleted unless requested)
      if (showDeleted || status !== TenantStatus.DELETED) {
        tierCounts[tier] = (tierCounts[tier] || 0) + 1;
      }

      // Count by status
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    res.status(200).json({
      status: 'success',
      data: {
        totalTenants: activeTenantCount,
        totalIncludingDeleted: allTenantsSnapshot.size,
        tierCounts,
        statusCounts,
      },
    });
  } catch (error) {
    logger.error(`Error getting tenant statistics: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tenant statistics',
    });
  }
});

// Get historical usage trends
router.get('/usage/history', authenticateJWT, async (req, res) => {
  try {
    // Parse query parameters
    const months = parseInt(req.query.months as string) || 6; // Default to 6 months
    const tenantId = req.query.tenantId as string; // Optional tenant filter

    // Validate months parameter
    if (months < 1 || months > 24) {
      return res.status(400).json({
        status: 'error',
        message: 'Months parameter must be between 1 and 24',
      });
    }

    // Calculate date range
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    // Initialize result arrays
    const monthlyData: Array<{
      month: string;
      year: number;
      usage: number;
      label: string;
    }> = [];

    // Generate month/year combinations for the requested period
    for (let i = 0; i < months; i++) {
      // Calculate month and year (going backwards from current month)
      let targetMonth = currentMonth - i;
      let targetYear = currentYear;

      // Adjust for previous years
      while (targetMonth <= 0) {
        targetMonth += 12;
        targetYear -= 1;
      }

      monthlyData.push({
        month: targetMonth.toString().padStart(2, '0'),
        year: targetYear,
        usage: 0,
        label: `${getMonthName(targetMonth)} ${targetYear}`
      });
    }

    // Reverse the array to get chronological order
    monthlyData.reverse();

    // Create an array of document IDs to fetch
    const docIds: string[] = [];

    if (tenantId) {
      // If tenant ID is provided, fetch only for that tenant
      monthlyData.forEach(item => {
        docIds.push(`${tenantId}_${item.year}_${item.month}`);
      });
    } else {
      // If no tenant ID, we'll fetch all usage docs for the time period
      const startDate = new Date(monthlyData[0].year, parseInt(monthlyData[0].month) - 1, 1);
      const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);

      // Get all usage documents in the date range
      const usageSnapshot = await db.collection('USAGE')
        .where('lastUpdated', '>=', startDate)
        .where('lastUpdated', '<', endDate)
        .get();

      // Process the results
      const usageByPeriod: Record<string, number> = {};

      usageSnapshot.forEach(doc => {
        const data = doc.data();
        const docParts = doc.id.split('_');

        if (docParts.length === 3) {
          // We don't need the tenant ID for aggregation by period
          const year = docParts[1];
          const month = docParts[2];
          const periodKey = `${year}_${month}`;

          // Aggregate usage by period
          usageByPeriod[periodKey] = (usageByPeriod[periodKey] || 0) + (data.count || 0);
        }
      });

      // Update the monthly data with the aggregated usage
      monthlyData.forEach(item => {
        const periodKey = `${item.year}_${item.month}`;
        item.usage = usageByPeriod[periodKey] || 0;
      });
    }

    // If we have a specific tenant, fetch the documents directly
    if (tenantId && docIds.length > 0) {
      // Firestore doesn't support getting multiple documents by ID in one query
      // So we need to fetch them individually
      const fetchPromises = docIds.map(async docId => {
        const docRef = db.collection('USAGE').doc(docId);
        return docRef.get();
      });

      const docSnapshots = await Promise.all(fetchPromises);

      // Process the results
      docSnapshots.forEach((snapshot, index) => {
        if (snapshot.exists) {
          const data = snapshot.data();
          monthlyData[index].usage = data?.count || 0;
        }
      });
    }

    // Calculate total usage and month-over-month growth
    const totalUsage = monthlyData.reduce((sum, item) => sum + item.usage, 0);
    const usageTrend = monthlyData.map((item, index, array) => {
      if (index === 0) return { ...item, growth: 0 };

      const previousUsage = array[index - 1].usage;
      const growth = previousUsage === 0
        ? 0
        : ((item.usage - previousUsage) / previousUsage) * 100;

      return {
        ...item,
        growth: Math.round(growth * 100) / 100 // Round to 2 decimal places
      };
    });

    res.status(200).json({
      status: 'success',
      data: {
        totalUsage,
        monthlyData: usageTrend,
        months,
        tenantId: tenantId || 'all'
      },
    });
  } catch (error) {
    logger.error(`Error getting historical usage trends: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get historical usage trends',
    });
  }
});

// Get tenant performance metrics
router.get('/tenant-metrics', authenticateJWT, async (req, res) => {
  try {
    // Parse query parameters
    const days = parseInt(req.query.days as string) || 30; // Default to 30 days
    const tenantId = req.query.tenantId as string; // Optional tenant filter

    // Validate days parameter
    if (days < 1 || days > 365) {
      return res.status(400).json({
        status: 'error',
        message: 'Days parameter must be between 1 and 365',
      });
    }

    // Calculate date range
    const now = new Date();
    const startDate = new Date(now);
    startDate.setDate(startDate.getDate() - days);

    // Build query
    let batchesQuery = db.collection('BATCHES')
      .where('status', '==', 'completed')
      .where('completedAt', '>=', startDate)
      .orderBy('completedAt', 'desc');

    // Add tenant filter if provided
    if (tenantId) {
      batchesQuery = batchesQuery.where('teamId', '==', tenantId);
    }

    // Execute query
    const batchesSnapshot = await batchesQuery.get();

    // Process results
    const batches = batchesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as BatchData[];

    // Calculate metrics
    const tenantMetrics: Record<string, {
      totalBatches: number;
      averageScore: number;
      highPriorityCount: number;
      maybeCount: number;
      passCount: number;
      processingTimes?: number[];
      averageProcessingTime: number;
      latestBatch: any;
    }> = {};

    batches.forEach(batch => {
      const teamId = batch.teamId || 'unknown';

      // Initialize tenant metrics if not exists
      if (!tenantMetrics[teamId]) {
        tenantMetrics[teamId] = {
          totalBatches: 0,
          averageScore: 0,
          highPriorityCount: 0,
          maybeCount: 0,
          passCount: 0,
          processingTimes: [],
          averageProcessingTime: 0,
          latestBatch: null
        };
      }

      // Update metrics
      tenantMetrics[teamId].totalBatches++;

      // Add score to calculation
      if (typeof batch.score === 'number') {
        const currentTotal = tenantMetrics[teamId].averageScore * (tenantMetrics[teamId].totalBatches - 1);
        tenantMetrics[teamId].averageScore = (currentTotal + batch.score) / tenantMetrics[teamId].totalBatches;
      }

      // Count by recommendation
      if (batch.recommendation === 'High Priority') {
        tenantMetrics[teamId].highPriorityCount++;
      } else if (batch.recommendation === 'Maybe') {
        tenantMetrics[teamId].maybeCount++;
      } else if (batch.recommendation === 'Pass') {
        tenantMetrics[teamId].passCount++;
      }

      // Calculate processing time if available
      if (batch.completedAt && batch.createdAt) {
        const completedAt = toDate(batch.completedAt);
        const createdAt = toDate(batch.createdAt);
        const processingTime = (completedAt.getTime() - createdAt.getTime()) / 1000; // in seconds

        // Ensure processingTimes array exists before pushing
        if (!tenantMetrics[teamId].processingTimes) {
          tenantMetrics[teamId].processingTimes = [];
        }
        tenantMetrics[teamId].processingTimes.push(processingTime);
      }

      // Store latest batch
      if (!tenantMetrics[teamId].latestBatch) {
        tenantMetrics[teamId].latestBatch = batch;
      }
    });

    // Calculate average processing times
    Object.keys(tenantMetrics).forEach(teamId => {
      const times = tenantMetrics[teamId].processingTimes || [];
      tenantMetrics[teamId].averageProcessingTime = times.length > 0
        ? times.reduce((sum, time) => sum + time, 0) / times.length
        : 0;

      // Round average score to 2 decimal places
      tenantMetrics[teamId].averageScore = Math.round(tenantMetrics[teamId].averageScore * 100) / 100;

      // Clean up the metrics object
      if (tenantMetrics[teamId].processingTimes) {
        delete tenantMetrics[teamId].processingTimes;
      }
    });

    res.status(200).json({
      status: 'success',
      data: {
        days,
        tenantId: tenantId || 'all',
        tenantMetrics,
        totalProcessed: batches.length
      },
    });
  } catch (error) {
    logger.error(`Error getting tenant performance metrics: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tenant performance metrics',
    });
  }
});

// Get system health metrics
router.get('/system-health', authenticateJWT, async (req, res) => {
  try {
    // Parse query parameters
    const hours = parseInt(req.query.hours as string) || 24; // Default to 24 hours

    // Validate hours parameter
    if (hours < 1 || hours > 168) { // Max 1 week
      return res.status(400).json({
        status: 'error',
        message: 'Hours parameter must be between 1 and 168',
      });
    }

    // Calculate date range
    const now = new Date();
    const startDate = new Date(now);
    startDate.setHours(startDate.getHours() - hours);

    // Get batches for the time period
    const batchesSnapshot = await db.collection('BATCHES')
      .where('createdAt', '>=', startDate)
      .orderBy('createdAt', 'desc')
      .get();

    const batches = batchesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as BatchData[];

    // Calculate metrics
    const totalBatches = batches.length;
    const completedBatches = batches.filter(batch => batch.status === 'completed').length;
    const failedBatches = batches.filter(batch => batch.status === 'failed').length;
    const pendingBatches = batches.filter(batch => batch.status === 'pending').length;
    const processingBatches = batches.filter(batch => batch.status === 'processing').length;

    // Calculate success rate
    const successRate = totalBatches > 0
      ? (completedBatches / totalBatches) * 100
      : 0;

    // Calculate average processing time for completed batches
    const processingTimes: number[] = [];
    batches.forEach(batch => {
      if (batch.status === 'completed' && batch.completedAt && batch.createdAt) {
        const completedAt = toDate(batch.completedAt);
        const createdAt = toDate(batch.createdAt);
        const processingTime = (completedAt.getTime() - createdAt.getTime()) / 1000; // in seconds
        processingTimes.push(processingTime);
      }
    });

    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
      : 0;

    // Get hourly breakdown
    const hourlyBreakdown: Array<{
      hour: string;
      total: number;
      completed: number;
      failed: number;
      averageProcessingTime: number;
    }> = [];

    for (let i = 0; i < hours; i++) {
      const hourStart = new Date(now);
      hourStart.setHours(hourStart.getHours() - i - 1);

      const hourEnd = new Date(now);
      hourEnd.setHours(hourEnd.getHours() - i);

      const hourBatches = batches.filter(batch => {
        const createdAt = toDate(batch.createdAt);
        return createdAt >= hourStart && createdAt < hourEnd;
      });

      const hourCompleted = hourBatches.filter(batch => batch.status === 'completed').length;
      const hourFailed = hourBatches.filter(batch => batch.status === 'failed').length;

      // Calculate average processing time for this hour
      const hourProcessingTimes: number[] = [];
      hourBatches.forEach(batch => {
        if (batch.status === 'completed' && batch.completedAt && batch.createdAt) {
          const completedAt = toDate(batch.completedAt);
          const createdAt = toDate(batch.createdAt);
          const processingTime = (completedAt.getTime() - createdAt.getTime()) / 1000; // in seconds
          hourProcessingTimes.push(processingTime);
        }
      });

      const hourAverageProcessingTime = hourProcessingTimes.length > 0
        ? hourProcessingTimes.reduce((sum, time) => sum + time, 0) / hourProcessingTimes.length
        : 0;

      hourlyBreakdown.push({
        hour: `${hourStart.getHours().toString().padStart(2, '0')}:00 - ${hourEnd.getHours().toString().padStart(2, '0')}:00`,
        total: hourBatches.length,
        completed: hourCompleted,
        failed: hourFailed,
        averageProcessingTime: Math.round(hourAverageProcessingTime * 100) / 100
      });
    }

    // Reverse to get chronological order
    hourlyBreakdown.reverse();

    res.status(200).json({
      status: 'success',
      data: {
        hours,
        totalBatches,
        completedBatches,
        failedBatches,
        pendingBatches,
        processingBatches,
        successRate: Math.round(successRate * 100) / 100,
        averageProcessingTime: Math.round(averageProcessingTime * 100) / 100,
        hourlyBreakdown
      },
    });
  } catch (error) {
    logger.error(`Error getting system health metrics: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get system health metrics',
    });
  }
});

// Get dashboard overview data
router.get('/dashboard', authenticateJWT, async (_req, res) => {
  try {
    // Get tenant counts
    const tenantsSnapshot = await db.collection('TENANTS').count().get();
    const totalTenants = tenantsSnapshot.data().count;

    // Count active tenants (those with at least one completed batch)
    const activeTenantsQuery = await db.collection('BATCHES')
      .where('status', '==', 'completed')
      .get();

    // Use a Set to count unique teamIds
    const activeTenantsSet = new Set();
    activeTenantsQuery.forEach(doc => {
      const data = doc.data();
      if (data.teamId) {
        activeTenantsSet.add(data.teamId);
      }
    });
    const activeTenants = activeTenantsSet.size;

    // Get total analyses count
    const analysesSnapshot = await db.collection('BATCHES').count().get();
    const totalAnalyses = analysesSnapshot.data().count;

    // Calculate average processing time
    const completedBatchesQuery = await db.collection('BATCHES')
      .where('status', '==', 'completed')
      .get();

    let totalProcessingTime = 0;
    let completedCount = 0;

    completedBatchesQuery.forEach(doc => {
      const data = doc.data();
      if (data.completedAt && data.createdAt) {
        const completedAt = toDate(data.completedAt);
        const createdAt = toDate(data.createdAt);
        const processingTime = (completedAt.getTime() - createdAt.getTime()) / 1000; // in seconds
        totalProcessingTime += processingTime;
        completedCount++;
      }
    });

    const avgProcessingTime = completedCount > 0
      ? Math.round((totalProcessingTime / completedCount) * 10) / 10
      : 0;

    // Get system health status
    const now = new Date();
    const startDate = new Date(now);
    startDate.setHours(startDate.getHours() - 24); // Last 24 hours

    const recentBatchesQuery = await db.collection('BATCHES')
      .where('createdAt', '>=', startDate)
      .get();

    const recentBatches = recentBatchesQuery.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as BatchData[];

    const recentCompleted = recentBatches.filter(batch => batch.status === 'completed').length;
    const recentFailed = recentBatches.filter(batch => batch.status === 'failed').length;
    const recentTotal = recentBatches.length;

    // Calculate success rate based on completed vs total batches
    const successRate = recentTotal > 0
      ? (recentCompleted / recentTotal) * 100
      : 100;

    // Determine system health status based on success rate and failed batches
    let healthStatus = 'healthy';
    let healthMessage = 'All services are operating normally';

    if (recentFailed > 0) {
      // If there are any failed batches, adjust health status
      if (recentFailed > recentCompleted) {
        healthStatus = 'critical';
        healthMessage = 'Critical system issues detected';
      } else if (recentFailed > 0) {
        healthStatus = 'warning';
        healthMessage = 'Some services are experiencing issues';
      }
    } else if (successRate < 90) {
      // If no failed batches but success rate is low (pending/processing batches)
      healthStatus = 'warning';
      healthMessage = 'Some services are experiencing delays';
    }

    const systemHealth = {
      status: healthStatus,
      message: healthMessage
    };

    res.status(200).json({
      status: 'success',
      data: {
        totalTenants,
        activeTenants,
        totalAnalyses,
        avgProcessingTime,
        systemHealth
      },
    });
  } catch (error) {
    logger.error(`Error getting dashboard data: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get dashboard data',
    });
  }
});

// Get recent analyses
router.get('/recent-analyses', authenticateJWT, async (req, res) => {
  try {
    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 4; // Default to 4 recent analyses

    // Validate limit parameter
    if (limit < 1 || limit > 50) {
      return res.status(400).json({
        status: 'error',
        message: 'Limit parameter must be between 1 and 50',
      });
    }

    // Get recent completed analyses
    // First try with completedAt field
    let recentAnalysesSnapshot;
    try {
      recentAnalysesSnapshot = await db.collection('BATCHES')
        .where('status', '==', 'completed')
        .orderBy('completedAt', 'desc')
        .limit(limit)
        .get();
    } catch (error) {
      try {
        // If that fails, try with updatedAt field instead
        logger.warn(`Error querying by completedAt, falling back to updatedAt: ${error}`);
        recentAnalysesSnapshot = await db.collection('BATCHES')
          .where('status', '==', 'completed')
          .orderBy('updatedAt', 'desc')
          .limit(limit)
          .get();
      } catch (error2) {
        // If that also fails, just get all completed batches without ordering
        logger.warn(`Error querying by updatedAt, getting all completed batches: ${error2}`);
        recentAnalysesSnapshot = await db.collection('BATCHES')
          .where('status', '==', 'completed')
          .limit(limit)
          .get();
      }
    }

    // Format the analyses for the frontend
    const recentAnalyses = recentAnalysesSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        companyName: data.companyName || 'Unknown Company',
        tenantName: data.teamId || 'Unknown Tenant', // Ideally, we'd look up the tenant name
        score: data.score || 0,
        timestamp: data.completedAt ? toDate(data.completedAt).toISOString() :
                  data.updatedAt ? toDate(data.updatedAt).toISOString() :
                  data.createdAt ? toDate(data.createdAt).toISOString() : null,
        recommendation: data.recommendation || 'Unknown',
        industrySector: data.industrySector || [],
        memoUrl: data.memoUrl || null
      };
    });

    // Try to replace tenant IDs with tenant names where possible
    const tenantIds = new Set(recentAnalyses.map(analysis => analysis.tenantName));
    const tenantSnapshots = await Promise.all(
      Array.from(tenantIds).map(id =>
        db.collection('TENANTS').doc(id.toString()).get()
      )
    );

    const tenantMap: Record<string, string> = {};
    tenantSnapshots.forEach(snapshot => {
      if (snapshot.exists) {
        const data = snapshot.data();
        if (data) {
          tenantMap[snapshot.id] = data.slackTeamName || data.name || snapshot.id;
        }
      }
    });

    // Update tenant names in the analyses
    const formattedAnalyses = recentAnalyses.map(analysis => ({
      ...analysis,
      tenantName: tenantMap[analysis.tenantName as string] || analysis.tenantName
    }));

    res.status(200).json({
      status: 'success',
      data: formattedAnalyses
    });
  } catch (error) {
    logger.error(`Error getting recent analyses: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get recent analyses',
    });
  }
});

export default router;
