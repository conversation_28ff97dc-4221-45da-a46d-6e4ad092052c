import { Router } from 'express';
import { getFirestore } from 'firebase-admin/firestore';
import jwt from 'jsonwebtoken';
import { authenticateJWT, requireAdmin } from '../middleware/auth';
import { logger } from '../utils/logger';

// Initialize Firestore
const db = getFirestore();

// Create router
const router = Router();

// Login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing email or password',
      });
    }
    
    // Get user by email
    const usersSnapshot = await db.collection('ADMIN_USERS')
      .where('email', '==', email)
      .limit(1)
      .get();
    
    if (usersSnapshot.empty) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password',
      });
    }
    
    const userDoc = usersSnapshot.docs[0];
    const userData = userDoc.data();
    
    // In a real application, you would hash the password and compare it
    // For simplicity, we're comparing plain text passwords here
    if (userData.password !== password) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password',
      });
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { id: userDoc.id, email: userData.email, role: userData.role },
      process.env.JWT_SECRET || 'valurize-secret-key',
      { expiresIn: '24h' }
    );
    
    res.status(200).json({
      status: 'success',
      data: {
        token,
        user: {
          id: userDoc.id,
          email: userData.email,
          role: userData.role,
        },
      },
    });
  } catch (error) {
    logger.error(`Error logging in: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to login',
    });
  }
});

// Get all users
router.get('/', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const usersSnapshot = await db.collection('ADMIN_USERS').get();
    
    const users = usersSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        email: data.email,
        role: data.role,
        createdAt: data.createdAt,
      };
    });
    
    res.status(200).json({
      status: 'success',
      data: users,
    });
  } catch (error) {
    logger.error(`Error getting users: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get users',
    });
  }
});

// Get user by ID
router.get('/:id', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const userDoc = await db.collection('ADMIN_USERS').doc(id).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found',
      });
    }
    
    const data = userDoc.data();
    
    res.status(200).json({
      status: 'success',
      data: {
        id: userDoc.id,
        email: data?.email,
        role: data?.role,
        createdAt: data?.createdAt,
      },
    });
  } catch (error) {
    logger.error(`Error getting user: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get user',
    });
  }
});

// Create user
router.post('/', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { email, password, role } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: email, password',
      });
    }
    
    // Check if user already exists
    const existingUsersSnapshot = await db.collection('ADMIN_USERS')
      .where('email', '==', email)
      .limit(1)
      .get();
    
    if (!existingUsersSnapshot.empty) {
      return res.status(400).json({
        status: 'error',
        message: 'User with this email already exists',
      });
    }
    
    // Create user
    const now = new Date();
    
    const newUserRef = await db.collection('ADMIN_USERS').add({
      email,
      password, // In a real application, you would hash this password
      role: role || 'user',
      createdAt: now,
      updatedAt: now,
    });
    
    res.status(201).json({
      status: 'success',
      data: {
        id: newUserRef.id,
        email,
        role: role || 'user',
        createdAt: now,
      },
    });
  } catch (error) {
    logger.error(`Error creating user: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create user',
    });
  }
});

// Update user
router.put('/:id', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { email, password, role } = req.body;
    
    // Check if user exists
    const userDoc = await db.collection('ADMIN_USERS').doc(id).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found',
      });
    }
    
    // Update user
    const updateData: Record<string, any> = {
      updatedAt: new Date(),
    };
    
    if (email) updateData.email = email;
    if (password) updateData.password = password; // In a real application, you would hash this password
    if (role) updateData.role = role;
    
    await db.collection('ADMIN_USERS').doc(id).update(updateData);
    
    const updatedUserDoc = await db.collection('ADMIN_USERS').doc(id).get();
    const updatedData = updatedUserDoc.data();
    
    res.status(200).json({
      status: 'success',
      data: {
        id,
        email: updatedData?.email,
        role: updatedData?.role,
        createdAt: updatedData?.createdAt,
      },
    });
  } catch (error) {
    logger.error(`Error updating user: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update user',
    });
  }
});

// Delete user
router.delete('/:id', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if user exists
    const userDoc = await db.collection('ADMIN_USERS').doc(id).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found',
      });
    }
    
    // Delete user
    await db.collection('ADMIN_USERS').doc(id).delete();
    
    res.status(200).json({
      status: 'success',
      message: 'User deleted successfully',
    });
  } catch (error) {
    logger.error(`Error deleting user: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete user',
    });
  }
});

export default router;
