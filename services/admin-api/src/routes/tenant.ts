import { Router } from 'express';
import { getFirestore } from 'firebase-admin/firestore';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { authenticateJWT, requireAdmin } from '../middleware/auth';
import { logger } from '../utils/logger';
import { generateSlackInstallUrl, generateNotionConnectUrl } from '../utils/urlGenerator';
import { getTenantOnboardingStatus, validateTenantData, getTierQuota } from '../utils/tenantUtils';
import { TenantStatus } from '../types/tenant';

// Initialize Firestore
const db = getFirestore();

// Initialize Secret Manager
const secretManager = new SecretManagerServiceClient();

// Create router
const router = Router();

// Get all tenants
router.get('/', authenticateJWT, async (req, res) => {
  try {
    const { includeDeleted = 'false' } = req.query;
    const showDeleted = includeDeleted === 'true';

    // Create query for tenants
    let query: FirebaseFirestore.Query<FirebaseFirestore.DocumentData> = db.collection('TENANTS');

    // Filter out deleted tenants unless explicitly requested
    if (!showDeleted) {
      query = query.where('status', '!=', TenantStatus.DELETED);
    }

    const tenantsSnapshot = await query.get();

    const tenants = tenantsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));

    res.status(200).json({
      status: 'success',
      data: tenants,
    });
  } catch (error) {
    logger.error(`Error getting tenants: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tenants',
    });
  }
});

// Get tenant by ID
router.get('/:id', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;

    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        id: tenantDoc.id,
        ...tenantDoc.data(),
      },
    });
  } catch (error) {
    logger.error(`Error getting tenant: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tenant',
    });
  }
});

// Create tenant
router.post('/', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const {
      id,
      tier,
      prompt,
      thresholds,
      registeredEmails,
      registeredDomains,
      extraQuota,
      companyName,
      contactEmail,
      contactName,
      status
    } = req.body;

    // Validate tenant data
    const validationError = validateTenantData(id, tier, status);
    if (validationError) {
      return res.status(400).json({
        status: 'error',
        message: validationError,
      });
    }

    // Check if tenant already exists
    const existingTenant = await db.collection('TENANTS').doc(id).get();

    if (existingTenant.exists) {
      return res.status(400).json({
        status: 'error',
        message: 'Tenant already exists',
      });
    }

    // Create tenant
    const now = new Date();
    const tierQuota = getTierQuota(tier);

    const tenantData = {
      tier,
      prompt: prompt || 'Analyze this pitch deck',
      thresholds: thresholds || { notionSync: 70 },
      registeredEmails: registeredEmails || [],
      registeredDomains: registeredDomains || [],
      extraQuota: extraQuota || 0,
      quota: tierQuota + (extraQuota || 0),
      companyName: companyName || '',
      contactEmail: contactEmail || '',
      contactName: contactName || '',
      slackConnected: false,
      notionConnected: false,
      status: status || TenantStatus.ACTIVE,
      joinDate: now,
      createdAt: now,
      updatedAt: now,
    };

    await db.collection('TENANTS').doc(id).set(tenantData);

    // Initialize usage record for the current month
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();
    const cycleKey = `${id}_${currentYear}_${currentMonth}`;

    await db.collection('USAGE').doc(cycleKey).set({
      count: 0,
      lastUpdated: now,
    });

    res.status(201).json({
      status: 'success',
      data: {
        id,
        ...tenantData,
      },
    });
  } catch (error) {
    logger.error(`Error creating tenant: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create tenant',
    });
  }
});

// Update tenant
router.put('/:id', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { tier, prompt, thresholds, registeredEmails, registeredDomains, status } = req.body;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    // Update tenant
    const updateData: Record<string, any> = {
      updatedAt: new Date(),
    };

    if (tier) updateData.tier = tier;
    if (prompt) updateData.prompt = prompt;
    if (thresholds) updateData.thresholds = thresholds;
    if (registeredEmails) updateData.registeredEmails = registeredEmails;
    if (registeredDomains) updateData.registeredDomains = registeredDomains;
    if (status) {
      // Validate status
      const validationError = validateTenantData(id, tier || tenantDoc.data()?.tier, status);
      if (validationError) {
        return res.status(400).json({
          status: 'error',
          message: validationError,
        });
      }
      updateData.status = status;
    }

    await db.collection('TENANTS').doc(id).update(updateData);

    res.status(200).json({
      status: 'success',
      data: {
        id,
        ...tenantDoc.data(),
        ...updateData,
      },
    });
  } catch (error) {
    logger.error(`Error updating tenant: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update tenant',
    });
  }
});

// Delete tenant
router.delete('/:id', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { soft = 'true' } = req.query; // Default to soft delete
    const isSoftDelete = soft === 'true';

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    if (isSoftDelete) {
      // Soft delete - update status to DELETED
      const now = new Date();
      await db.collection('TENANTS').doc(id).update({
        status: TenantStatus.DELETED,
        deletedAt: now,
        updatedAt: now,
      });

      res.status(200).json({
        status: 'success',
        message: 'Tenant soft-deleted successfully',
      });
    } else {
      // Hard delete - remove from database
      await db.collection('TENANTS').doc(id).delete();

      res.status(200).json({
        status: 'success',
        message: 'Tenant permanently deleted successfully',
      });
    }
  } catch (error) {
    logger.error(`Error deleting tenant: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete tenant',
    });
  }
});

// Get tenant usage
router.get('/:id/usage', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    // Get current month and year
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;

    // Get usage for current month
    const usageDoc = await db.collection('USAGE').doc(`${id}_${year}_${month}`).get();

    const usage = usageDoc.exists ? usageDoc.data() : { count: 0, lastUpdated: null };

    res.status(200).json({
      status: 'success',
      data: {
        tenant: id,
        year,
        month,
        usage,
      },
    });
  } catch (error) {
    logger.error(`Error getting tenant usage: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tenant usage',
    });
  }
});

// Reset tenant usage
router.post('/:id/reset-usage', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    // Get current month and year
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;

    // Reset usage for current month
    await db.collection('USAGE').doc(`${id}_${year}_${month}`).set({
      count: 0,
      lastUpdated: now,
    });

    res.status(200).json({
      status: 'success',
      message: 'Tenant usage reset successfully',
    });
  } catch (error) {
    logger.error(`Error resetting tenant usage: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to reset tenant usage',
    });
  }
});

// Add usage credits
router.post('/:id/add-credits', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { credits } = req.body;

    if (!credits || typeof credits !== 'number' || credits <= 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid credits value',
      });
    }

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    // Get current month and year
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;

    // Get current usage
    const usageDoc = await db.collection('USAGE').doc(`${id}_${year}_${month}`).get();

    const currentCount = usageDoc.exists && usageDoc.data()?.count ? usageDoc.data()?.count : 0;

    // Update usage with negative count (adding credits)
    await db.collection('USAGE').doc(`${id}_${year}_${month}`).set({
      count: currentCount - credits,
      lastUpdated: now,
    });

    res.status(200).json({
      status: 'success',
      message: `Added ${credits} credits to tenant ${id}`,
    });
  } catch (error) {
    logger.error(`Error adding credits: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to add credits',
    });
  }
});

// Get tenant onboarding status
router.get('/:id/onboarding-status', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;

    // Get tenant onboarding status
    const status = await getTenantOnboardingStatus(id);

    if (!status) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    res.status(200).json({
      status: 'success',
      data: status,
    });
  } catch (error) {
    logger.error(`Error getting tenant onboarding status: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get tenant onboarding status',
    });
  }
});

// Generate Slack installation URL
router.get('/:id/slack-install-url', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    // Generate Slack installation URL
    const slackInstallUrl = generateSlackInstallUrl(id);

    res.status(200).json({
      status: 'success',
      data: {
        slackInstallUrl,
      },
    });
  } catch (error) {
    logger.error(`Error generating Slack install URL: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate Slack install URL',
    });
  }
});

// Generate Notion connection URL
router.get('/:id/notion-connect-url', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    // Generate Notion connection URL
    const notionConnectUrl = generateNotionConnectUrl(id);

    res.status(200).json({
      status: 'success',
      data: {
        notionConnectUrl,
      },
    });
  } catch (error) {
    logger.error(`Error generating Notion connect URL: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate Notion connect URL',
    });
  }
});

// Update tenant status
router.post('/:id/status', authenticateJWT, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        status: 'error',
        message: 'Status is required',
      });
    }

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(id).get();

    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }

    const tenantData = tenantDoc.data();

    // Validate status
    const validationError = validateTenantData(id, tenantData?.tier, status);
    if (validationError) {
      return res.status(400).json({
        status: 'error',
        message: validationError,
      });
    }

    // Update tenant status
    const now = new Date();
    const updateData: Record<string, any> = {
      status,
      updatedAt: now,
    };

    // If status is DELETED, add deletedAt timestamp
    if (status === TenantStatus.DELETED) {
      updateData.deletedAt = now;
    } else if (tenantData?.deletedAt) {
      // If status is not DELETED but tenant was previously deleted, remove deletedAt
      updateData.deletedAt = null;
    }

    await db.collection('TENANTS').doc(id).update(updateData);

    res.status(200).json({
      status: 'success',
      message: `Tenant status updated to ${status}`,
      data: {
        id,
        status,
        updatedAt: now,
      },
    });
  } catch (error) {
    logger.error(`Error updating tenant status: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update tenant status',
    });
  }
});

export default router;
