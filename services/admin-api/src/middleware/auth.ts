import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { db } from '../utils/firebase';
import { logger } from '../utils/logger';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
      };
    }
  }
}

// Middleware to verify JWT token
export const authenticateJWT = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({ status: 'error', message: 'No authorization header provided' });
    }

    const token = authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ status: 'error', message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'valurize-secret-key') as jwt.JwtPayload;

    if (!decoded.id) {
      return res.status(401).json({ status: 'error', message: 'Invalid token' });
    }

    // Get user from Firestore
    const userDoc = await db.collection('ADMIN_USERS').doc(decoded.id).get();

    if (!userDoc.exists) {
      return res.status(401).json({ status: 'error', message: 'User not found' });
    }

    const userData = userDoc.data();

    if (!userData) {
      return res.status(401).json({ status: 'error', message: 'User data not found' });
    }

    // Attach user to request
    req.user = {
      id: userDoc.id,
      email: userData.email,
      role: userData.role,
    };

    next();
  } catch (error) {
    logger.error(`Authentication error: ${error}`);
    return res.status(401).json({ status: 'error', message: 'Invalid token' });
  }
};

// Middleware to check if user is an admin
export const requireAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ status: 'error', message: 'Authentication required' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ status: 'error', message: 'Admin access required' });
  }

  next();
};
