import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import dotenv from 'dotenv';
import multer from 'multer';
import { Storage } from '@google-cloud/storage';
import { PubSub } from '@google-cloud/pubsub';
import { v4 as uuidv4 } from 'uuid';
import formData from 'form-data';
import Mailgun from 'mailgun.js';
import { logger } from './utils/logger';
import { db } from './utils/firebase';

// Load environment variables
dotenv.config();

// Initialize Storage
const storage = new Storage();
const bucketName = process.env.PDF_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-pdfs`;
const bucket = storage.bucket(bucketName);

// Initialize PubSub
const pubsub = new PubSub();
const pdfProcessingTopic = pubsub.topic(process.env.PDF_PROCESSING_TOPIC || 'pdf-processing');

// Initialize Mailgun
const mailgun = new Mailgun(formData);
const mg = mailgun.client({
  username: 'api',
  key: process.env.MAILGUN_API_KEY || '',
});

// Create Express app
const app = express();
const port = process.env.PORT || 8080;

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Mailgun webhook endpoint
app.post('/webhook', upload.array('attachment'), async (req, res) => {
  try {
    logger.info('Received email webhook');

    // Verify Mailgun signature
    const signature = req.body.signature;
    if (!signature) {
      logger.warn('Missing Mailgun signature');
      return res.status(401).json({ status: 'error', message: 'Unauthorized' });
    }

    // Extract email data
    const sender = req.body.sender;
    const recipient = req.body.recipient;
    const subject = req.body.subject || 'No Subject';
    const teamId = await getTeamIdFromEmail(sender);

    if (!teamId) {
      logger.warn(`No team found for sender: ${sender}`);
      return res.status(200).json({ status: 'error', message: 'No team found for sender' });
    }

    // Check if there are attachments
    const files = req.files as Express.Multer.File[];
    if (!files || files.length === 0) {
      logger.warn('No attachments found');
      return res.status(200).json({ status: 'error', message: 'No attachments found' });
    }

    // Filter for PDF files
    const pdfFiles = files.filter(file => file.mimetype === 'application/pdf');
    if (pdfFiles.length === 0) {
      logger.warn('No PDF attachments found');

      // Send reply email
      await sendReplyEmail(sender, 'No PDF attachments found',
        'We could not find any PDF attachments in your email. Please attach PDF files and try again.');

      return res.status(200).json({ status: 'error', message: 'No PDF attachments found' });
    }

    logger.info(`Processing ${pdfFiles.length} PDF attachments`);

    // Process each PDF file
    const pdfList: string[] = [];

    for (const file of pdfFiles) {
      // Generate a unique ID for this file
      const batchId = uuidv4();

      // Upload to GCS
      const filename = `uploads/${teamId}/${batchId}.pdf`;
      await bucket.file(filename).save(file.buffer, {
        metadata: {
          contentType: 'application/pdf',
          metadata: {
            teamId,
            originalFilename: file.originalname,
            sender,
            recipient,
            subject,
          },
        },
      });

      // Create a batch record in Firestore
      const now = new Date();

      await db.collection('BATCHES').doc(batchId).set({
        teamId,
        userId: sender,
        filename: file.originalname,
        gcsPath: filename,
        status: 'pending',
        source: 'email',
        sender,
        recipient,
        subject,
        createdAt: now,
        updatedAt: now,
      });

      pdfList.push(batchId);

      logger.info(`Uploaded PDF ${file.originalname} with batch ID ${batchId}`);
    }

    // If we have PDFs to process, publish a message to the processing topic
    if (pdfList.length > 0) {
      await pdfProcessingTopic.publish(Buffer.from(JSON.stringify({
        teamId,
        userId: sender,
        pdfList,
      })));

      logger.info(`Published batch for team ${teamId} with ${pdfList.length} PDFs for processing`);

      // Send confirmation email
      await sendReplyEmail(sender, 'Pitch deck analysis in progress',
        `We've received your ${pdfList.length} pitch deck(s) and are analyzing them now. We'll send you the results when they're ready.`);
    }

    res.status(200).json({ status: 'success' });
  } catch (error) {
    logger.error(`Error processing email webhook: ${error}`);
    res.status(500).json({ status: 'error', message: 'Internal server error' });
  }
});

// Helper function to get team ID from email
async function getTeamIdFromEmail(email: string): Promise<string | null> {
  try {
    // Extract domain from email
    const domain = email.split('@')[1];

    if (!domain) {
      return null;
    }

    // First, check if the exact email is registered
    const emailTenantsSnapshot = await db.collection('TENANTS')
      .where('registeredEmails', 'array-contains', email)
      .limit(1)
      .get();

    if (!emailTenantsSnapshot.empty) {
      return emailTenantsSnapshot.docs[0].id;
    }

    // Then, check if the domain is registered
    const domainTenantsSnapshot = await db.collection('TENANTS')
      .where('registeredDomains', 'array-contains', domain)
      .limit(1)
      .get();

    if (!domainTenantsSnapshot.empty) {
      return domainTenantsSnapshot.docs[0].id;
    }

    return null;
  } catch (error) {
    logger.error(`Error getting team ID from email: ${error}`);
    return null;
  }
}

// Helper function to send reply email
async function sendReplyEmail(to: string, subject: string, text: string): Promise<void> {
  try {
    const domain = process.env.MAILGUN_DOMAIN || '';

    await mg.messages.create(domain, {
      from: `Valurize <noreply@${domain}>`,
      to: [to],
      subject,
      text,
    });

    logger.info(`Sent reply email to ${to}`);
  } catch (error) {
    logger.error(`Error sending reply email: ${error}`);
  }
}

// Start the server
app.listen(port, () => {
  logger.info(`Email ingestion service running on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});
