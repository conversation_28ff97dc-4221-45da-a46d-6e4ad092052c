{"name": "email-ingest", "version": "1.0.0", "description": "Email ingestion service for Valurize", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev src/index.ts", "test": "jest"}, "dependencies": {"@google-cloud/pubsub": "^3.7.0", "@google-cloud/secret-manager": "^4.2.2", "@google-cloud/storage": "^6.11.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "firebase-admin": "^11.8.0", "helmet": "^6.1.5", "mailgun.js": "^8.2.1", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/multer": "^1.4.7", "@types/node": "^18.16.3", "@types/uuid": "^9.0.1", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.0.4"}}