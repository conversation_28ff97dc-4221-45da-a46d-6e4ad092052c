import { Router } from 'express';
import { getFirestore } from 'firebase-admin/firestore';
import { Storage } from '@google-cloud/storage';
import { Client as NotionClient } from '@notionhq/client';
import { logger } from '../utils/logger';
import { getNotionToken } from '../utils/tokenService';
import { createNotionPage, validateDatabaseAccess, AnalysisResult } from '../utils/notionUtils';

const router = Router();
const db = getFirestore();
const storage = new Storage();
const resultsBucketName = process.env.RESULTS_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-results`;
const resultsBucket = storage.bucket(resultsBucketName);

// Manually sync a batch to Notion
router.post('/sync/:batchId', async (req, res) => {
  try {
    const { batchId } = req.params;
    
    // Get batch information
    const batchDoc = await db.collection('BATCHES').doc(batchId).get();
    
    if (!batchDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Batch not found',
      });
    }
    
    const batch = batchDoc.data();
    
    if (!batch) {
      return res.status(404).json({
        status: 'error',
        message: 'Batch data not found',
      });
    }
    
    const { teamId } = batch;
    
    // Get tenant information
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();
    
    if (!tenantDoc.exists) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant not found',
      });
    }
    
    const tenant = tenantDoc.data();
    
    if (!tenant) {
      return res.status(404).json({
        status: 'error',
        message: 'Tenant data not found',
      });
    }
    
    // Check if Notion is connected
    if (!tenant.notionConnected || !tenant.notionDatabaseId) {
      return res.status(400).json({
        status: 'error',
        message: 'Notion not configured for this tenant',
      });
    }
    
    // Get Notion token
    const notionToken = await getNotionToken(teamId);
    
    if (!notionToken) {
      return res.status(400).json({
        status: 'error',
        message: 'Notion token not found',
      });
    }
    
    // Initialize Notion client
    const notion = new NotionClient({ auth: notionToken.access_token });
    
    // Validate database access
    const hasAccess = await validateDatabaseAccess(notion, tenant.notionDatabaseId);
    
    if (!hasAccess) {
      return res.status(400).json({
        status: 'error',
        message: 'No access to Notion database',
      });
    }
    
    // Get analysis results from GCS
    const [fileContent] = await resultsBucket.file(batch.resultPath).download();
    const analysis = JSON.parse(fileContent.toString()) as AnalysisResult;
    
    // Create page in Notion database
    const pageId = await createNotionPage(
      notion,
      tenant.notionDatabaseId,
      analysis,
      tenant.notionFieldMappings || {}
    );
    
    // Update batch with Notion sync status
    await db.collection('BATCHES').doc(batchId).update({
      notionSynced: true,
      notionSyncedAt: new Date(),
      notionPageId: pageId,
      notionSyncError: false,
      notionSyncErrorAt: null,
      notionSyncErrorMessage: null,
    });
    
    res.status(200).json({
      status: 'success',
      message: 'Batch synced to Notion',
      data: {
        pageId,
      },
    });
  } catch (error) {
    logger.error(`Error manually syncing batch to Notion: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
    });
  }
});

// Retry failed syncs
router.post('/retry-failed', async (req, res) => {
  try {
    const { teamId } = req.body;
    
    if (!teamId) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing teamId',
      });
    }
    
    // Get failed batches
    const batchesSnapshot = await db.collection('BATCHES')
      .where('teamId', '==', teamId)
      .where('notionSyncError', '==', true)
      .get();
    
    if (batchesSnapshot.empty) {
      return res.status(200).json({
        status: 'success',
        message: 'No failed syncs found',
        data: {
          count: 0,
        },
      });
    }
    
    const batchIds = batchesSnapshot.docs.map(doc => doc.id);
    
    // Queue batches for retry
    await db.collection('RETRY_QUEUE').add({
      teamId,
      batchIds,
      createdAt: new Date(),
      status: 'pending',
    });
    
    res.status(200).json({
      status: 'success',
      message: 'Failed syncs queued for retry',
      data: {
        count: batchIds.length,
      },
    });
  } catch (error) {
    logger.error(`Error retrying failed syncs: ${error}`);
    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
    });
  }
});

export default router;
