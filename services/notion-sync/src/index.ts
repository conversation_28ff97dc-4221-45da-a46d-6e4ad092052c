import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import dotenv from 'dotenv';
import { Storage } from '@google-cloud/storage';
import { PubSub } from '@google-cloud/pubsub';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { Client as NotionClient } from '@notionhq/client';
import { logger } from './utils/logger';
import { createNotionPage, validateDatabaseAccess, AnalysisResult, FieldMappings } from './utils/notionUtils';
import { getNotionToken } from './utils/tokenService';
import { db } from './utils/firebase';

// Load environment variables
dotenv.config();

// Initialize Storage
const storage = new Storage();
const resultsBucketName = process.env.RESULTS_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-results`;
const resultsBucket = storage.bucket(resultsBucketName);

// Initialize Secret Manager
const secretManager = new SecretManagerServiceClient();

// Create Express app
const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Notion OAuth callback endpoint
app.get('/notion/callback', async (req, res) => {
  try {
    const { code, state } = req.query;

    if (!code || !state) {
      return res.status(400).send('Missing code or state parameter');
    }

    const teamId = state as string;

    // Exchange code for access token
    const response = await fetch('https://api.notion.com/v1/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${Buffer.from(`${process.env.NOTION_CLIENT_ID}:${process.env.NOTION_CLIENT_SECRET}`).toString('base64')}`,
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        code,
        redirect_uri: `${process.env.SERVICE_URL}/notion/callback`,
      }),
    });

    if (!response.ok) {
      logger.error(`Error exchanging code for token: ${await response.text()}`);
      return res.status(500).send('Error connecting to Notion');
    }

    const tokenData = await response.json();

    // Store access token in Secret Manager
    const secretName = `projects/${process.env.GOOGLE_CLOUD_PROJECT}/secrets/notion-token-${teamId}`;

    try {
      await secretManager.getSecret({ name: secretName });
    } catch (error) {
      // Secret doesn't exist, create it
      await secretManager.createSecret({
        parent: `projects/${process.env.GOOGLE_CLOUD_PROJECT}`,
        secretId: `notion-token-${teamId}`,
        secret: {
          replication: {
            automatic: {},
          },
        },
      });
    }

    // Add a new version of the secret
    await secretManager.addSecretVersion({
      parent: secretName,
      payload: {
        data: Buffer.from(JSON.stringify(tokenData)),
      },
    });

    // Update tenant with Notion connection status
    await db.collection('TENANTS').doc(teamId).update({
      notionConnected: true,
      notionWorkspaceId: tokenData.workspace_id,
      notionWorkspaceName: tokenData.workspace_name,
      notionBotId: tokenData.bot_id,
      notionUpdatedAt: new Date(),
    });

    // Redirect to success page
    res.redirect(`${process.env.FRONTEND_URL}/notion-success?teamId=${teamId}`);
  } catch (error) {
    logger.error(`Error handling Notion callback: ${error}`);
    res.status(500).send('Error connecting to Notion');
  }
});

// Database selection endpoint
app.post('/notion/select-database', async (req, res) => {
  try {
    const { teamId, databaseId, fieldMappings, threshold } = req.body;

    if (!teamId || !databaseId) {
      return res.status(400).json({ status: 'error', message: 'Missing teamId or databaseId' });
    }

    // Update tenant with database selection
    await db.collection('TENANTS').doc(teamId).update({
      notionDatabaseId: databaseId,
      notionFieldMappings: fieldMappings || {},
      notionThreshold: threshold || 70,
      notionDatabaseUpdatedAt: new Date(),
    });

    res.status(200).json({ status: 'success' });
  } catch (error) {
    logger.error(`Error selecting Notion database: ${error}`);
    res.status(500).json({ status: 'error', message: 'Internal server error' });
  }
});

// List databases endpoint
app.get('/notion/databases/:teamId', async (req, res) => {
  try {
    const { teamId } = req.params;

    // Get Notion token from Secret Manager
    const notionToken = await getNotionToken(teamId);

    if (!notionToken) {
      return res.status(404).json({ status: 'error', message: 'Notion connection not found' });
    }

    // Initialize Notion client
    const notion = new NotionClient({ auth: notionToken.access_token });

    // List databases
    const response = await notion.search({
      filter: {
        property: 'object',
        value: 'database',
      },
    });

    // Format response
    const databases = response.results.map((db: any) => ({
      id: db.id,
      title: db.title?.[0]?.plain_text || 'Untitled',
      url: db.url,
      properties: db.properties,
    }));

    res.status(200).json({ status: 'success', databases });
  } catch (error) {
    logger.error(`Error listing Notion databases: ${error}`);
    res.status(500).json({ status: 'error', message: 'Internal server error' });
  }
});

// Set up PubSub subscription
const subscriptionName = process.env.ANALYSIS_COMPLETE_SUBSCRIPTION || 'analysis-complete-subscription';
const subscription = new PubSub().subscription(subscriptionName);

subscription.on('message', async (message) => {
  try {
    const data = JSON.parse(message.data.toString());
    const { teamId, pdfList } = data;

    logger.info(`Processing analysis results for team ${teamId} with ${pdfList.length} PDFs`);

    // Get tenant information
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      logger.warn(`Tenant ${teamId} not found`);
      message.ack();
      return;
    }

    const tenant = tenantDoc.data();

    if (!tenant) {
      logger.warn(`Tenant data for ${teamId} not found`);
      message.ack();
      return;
    }

    // Check if Notion is connected
    if (!tenant.notionConnected || !tenant.notionDatabaseId) {
      logger.info(`Notion not configured for tenant ${teamId}`);
      message.ack();
      return;
    }

    // Get Notion token
    const notionToken = await getNotionToken(teamId);

    if (!notionToken) {
      logger.warn(`Notion token not found for tenant ${teamId}`);
      message.ack();
      return;
    }

    // Initialize Notion client
    const notion = new NotionClient({ auth: notionToken.access_token });

    // Validate database access
    const hasAccess = await validateDatabaseAccess(notion, tenant.notionDatabaseId);

    if (!hasAccess) {
      logger.error(`No access to Notion database ${tenant.notionDatabaseId} for team ${teamId}`);

      // Update tenant to indicate database access issue
      await db.collection('TENANTS').doc(teamId).update({
        notionDatabaseAccessError: true,
        notionDatabaseAccessErrorAt: new Date(),
      });

      message.ack();
      return;
    }

    // Process each PDF in the list
    const results = await Promise.allSettled(pdfList.map(async (batchId: string) => {
      try {
        // Get batch information
        const batchDoc = await db.collection('BATCHES').doc(batchId).get();

        if (!batchDoc.exists) {
          logger.warn(`Batch ${batchId} not found`);
          return { success: false, batchId, reason: 'Batch not found' };
        }

        const batch = batchDoc.data();

        if (!batch) {
          logger.warn(`Batch data for ${batchId} not found`);
          return { success: false, batchId, reason: 'Batch data not found' };
        }

        // Check if batch is completed
        if (batch.status !== 'completed') {
          logger.warn(`Batch ${batchId} is not completed (status: ${batch.status})`);
          return { success: false, batchId, reason: 'Batch not completed' };
        }

        // Check if already synced to Notion
        if (batch.notionSynced) {
          logger.info(`Batch ${batchId} already synced to Notion`);
          return { success: true, batchId, reason: 'Already synced' };
        }

        // Check if score meets threshold
        const threshold = tenant.notionThreshold || 70;

        if (batch.score < threshold) {
          logger.info(`Batch ${batchId} score (${batch.score}) is below threshold (${threshold})`);

          // Mark as processed but not synced due to threshold
          await db.collection('BATCHES').doc(batchId).update({
            notionSyncSkipped: true,
            notionSyncSkippedReason: 'Below threshold',
            notionSyncSkippedAt: new Date(),
          });

          return { success: true, batchId, reason: 'Below threshold' };
        }

        // Get analysis results from GCS
        const [fileContent] = await resultsBucket.file(batch.resultPath).download();
        const analysis = JSON.parse(fileContent.toString()) as AnalysisResult;

        // Create page in Notion database using the utility function
        const pageId = await createNotionPage(
          notion,
          tenant.notionDatabaseId,
          analysis,
          tenant.notionFieldMappings || {}
        );

        logger.info(`Added batch ${batchId} to Notion database (page ID: ${pageId}) for team ${teamId}`);

        // Update batch with Notion sync status
        await db.collection('BATCHES').doc(batchId).update({
          notionSynced: true,
          notionSyncedAt: new Date(),
          notionPageId: pageId,
        });

        return { success: true, batchId, pageId };
      } catch (error) {
        logger.error(`Error processing batch ${batchId}: ${error}`);

        // Record the error in Firestore for retry later
        try {
          await db.collection('BATCHES').doc(batchId).update({
            notionSyncError: true,
            notionSyncErrorAt: new Date(),
            notionSyncErrorMessage: String(error),
          });
        } catch (updateError) {
          logger.error(`Error updating batch ${batchId} with error status: ${updateError}`);
        }

        return { success: false, batchId, reason: String(error) };
      }
    }));

    // Log results summary
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success)).length;

    logger.info(`Notion sync complete for team ${teamId}: ${successful} successful, ${failed} failed`);

    // Acknowledge the message
    message.ack();
  } catch (error) {
    logger.error(`Error processing message: ${error}`);

    // Only nack if it's a temporary error that might be resolved by retrying
    const errorStr = String(error);
    if (errorStr.includes('RESOURCE_EXHAUSTED') ||
        errorStr.includes('UNAVAILABLE') ||
        errorStr.includes('DEADLINE_EXCEEDED')) {
      logger.info('Temporary error, nacking message for retry');
      message.nack();
    } else {
      // For permanent errors, ack the message to avoid infinite retries
      logger.warn('Permanent error, acknowledging message to avoid infinite retries');
      message.ack();
    }
  }
});

// Import routes
import manualRoutes from './routes/manual';

// Register routes
app.use('/manual', manualRoutes);

// Start the server
app.listen(port, () => {
  logger.info(`Notion sync service running on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});
