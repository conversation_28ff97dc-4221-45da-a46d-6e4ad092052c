import { Client as NotionClient } from '@notionhq/client';
import { logger } from './logger';

/**
 * Analysis result interface
 */
export interface AnalysisResult {
  companyName: string;
  score: number;
  summary: string;
  strengths: string[];
  weaknesses: string[];
  marketOpportunity: {
    size: string;
    growth: string;
    assessment: string;
  };
  team: {
    assessment: string;
    experience: string;
    gaps: string;
  };
  financials: {
    assessment: string;
    runway: string;
    projections: string;
  };
  investmentRecommendation: string;
  followUpQuestions: string[];
}

/**
 * Field mapping interface
 */
export interface FieldMappings {
  name?: string;
  score?: string;
  summary?: string;
  strengths?: string;
  weaknesses?: string;
  recommendation?: string;
  market?: string;
  team?: string;
  financials?: string;
  analyzed_date?: string;
  [key: string]: string | undefined;
}

/**
 * Creates a Notion page in the specified database with the analysis results
 * 
 * @param notion Notion client
 * @param databaseId Notion database ID
 * @param analysis Analysis result
 * @param fieldMappings Field mappings
 * @returns The created page ID
 */
export async function createNotionPage(
  notion: NotionClient,
  databaseId: string,
  analysis: AnalysisResult,
  fieldMappings: FieldMappings
): Promise<string> {
  try {
    // Map fields according to tenant configuration
    const properties: Record<string, any> = {};
    
    // Default mappings if not specified
    if (!fieldMappings.name) {
      properties['Name'] = { title: [{ text: { content: analysis.companyName || 'Unnamed Company' } }] };
    }
    
    if (!fieldMappings.score) {
      properties['Score'] = { number: analysis.score || 0 };
    }
    
    // Apply custom field mappings
    Object.entries(fieldMappings).forEach(([field, notionField]) => {
      if (!notionField) return;
      
      switch (field) {
        case 'name':
          properties[notionField] = { title: [{ text: { content: analysis.companyName || 'Unnamed Company' } }] };
          break;
        case 'score':
          properties[notionField] = { number: analysis.score || 0 };
          break;
        case 'summary':
          properties[notionField] = { rich_text: [{ text: { content: truncateText(analysis.summary || '', 2000) } }] };
          break;
        case 'strengths':
          properties[notionField] = { rich_text: [{ text: { content: truncateText(analysis.strengths?.join(', ') || '', 2000) } }] };
          break;
        case 'weaknesses':
          properties[notionField] = { rich_text: [{ text: { content: truncateText(analysis.weaknesses?.join(', ') || '', 2000) } }] };
          break;
        case 'recommendation':
          properties[notionField] = { rich_text: [{ text: { content: truncateText(analysis.investmentRecommendation || '', 2000) } }] };
          break;
        case 'market':
          properties[notionField] = { rich_text: [{ text: { content: truncateText(analysis.marketOpportunity?.assessment || '', 2000) } }] };
          break;
        case 'team':
          properties[notionField] = { rich_text: [{ text: { content: truncateText(analysis.team?.assessment || '', 2000) } }] };
          break;
        case 'financials':
          properties[notionField] = { rich_text: [{ text: { content: truncateText(analysis.financials?.assessment || '', 2000) } }] };
          break;
        case 'analyzed_date':
          properties[notionField] = { date: { start: new Date().toISOString() } };
          break;
      }
    });
    
    // Create page in Notion database
    const response = await notion.pages.create({
      parent: { database_id: databaseId },
      properties,
    });
    
    return response.id;
  } catch (error) {
    logger.error(`Error creating Notion page: ${error}`);
    throw error;
  }
}

/**
 * Truncates text to the specified length to avoid Notion API limits
 * 
 * @param text Text to truncate
 * @param maxLength Maximum length
 * @returns Truncated text
 */
function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Validates database access and permissions
 * 
 * @param notion Notion client
 * @param databaseId Database ID
 * @returns True if database is accessible, false otherwise
 */
export async function validateDatabaseAccess(
  notion: NotionClient,
  databaseId: string
): Promise<boolean> {
  try {
    await notion.databases.retrieve({ database_id: databaseId });
    return true;
  } catch (error) {
    logger.error(`Error validating database access: ${error}`);
    return false;
  }
}
