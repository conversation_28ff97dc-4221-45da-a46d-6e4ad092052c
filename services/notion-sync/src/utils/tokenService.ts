import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { logger } from './logger';

// Initialize Secret Manager
const secretManager = new SecretManagerServiceClient();

/**
 * Gets a Notion token for a team from Secret Manager
 * 
 * @param teamId The team ID
 * @returns The Notion token data or null if not found
 */
export async function getNotionToken(teamId: string): Promise<any | null> {
  try {
    const secretName = `projects/${process.env.GOOGLE_CLOUD_PROJECT}/secrets/notion-token-${teamId}/versions/latest`;
    const [version] = await secretManager.accessSecretVersion({ name: secretName });
    
    if (!version.payload || !version.payload.data) {
      return null;
    }
    
    return JSON.parse(version.payload.data.toString());
  } catch (error) {
    logger.error(`Error getting Notion token for team ${teamId}: ${error}`);
    return null;
  }
}
