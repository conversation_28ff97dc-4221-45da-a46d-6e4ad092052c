name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
  REGION: us-central1

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Lint
        run: npm run lint

      - name: Test
        run: npm test

  terraform:
    needs: lint-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.0.0

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ env.GCP_SA_KEY }}

      - name: Terraform Init
        run: |
          cd infra
          terraform init

      - name: Terraform Plan
        run: |
          cd infra
          terraform plan -var="project_id=${{ env.PROJECT_ID }}" -var="region=${{ env.REGION }}"

      - name: Terraform Apply
        run: |
          cd infra
          terraform apply -auto-approve -var="project_id=${{ env.PROJECT_ID }}" -var="region=${{ env.REGION }}"

  build-and-deploy:
    needs: terraform
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - service: tenant
            cpu: 1
            memory: 1Gi
            concurrency: 10
            min_instances: 1
            max_instances: 20
            allow_unauthenticated: true
          - service: slack-ingest
            cpu: 1
            memory: 512Mi
            concurrency: 50
            min_instances: 1
            max_instances: 100
            allow_unauthenticated: true
          - service: email-ingest
            cpu: 1
            memory: 512Mi
            concurrency: 50
            min_instances: 1
            max_instances: 100
            allow_unauthenticated: true
          - service: processor
            cpu: 2
            memory: 2Gi
            concurrency: 1
            min_instances: 1
            max_instances: 100
            allow_unauthenticated: false
            timeout: 900s
          - service: slack-notify
            cpu: 0.5
            memory: 512Mi
            concurrency: 10
            min_instances: 0
            max_instances: 20
            allow_unauthenticated: false
          - service: notion-sync
            cpu: 0.5
            memory: 512Mi
            concurrency: 10
            min_instances: 0
            max_instances: 20
            allow_unauthenticated: true
          - service: token-rotator
            cpu: 0.2
            memory: 128Mi
            concurrency: 1
            min_instances: 0
            max_instances: 1
            allow_unauthenticated: false
          - service: admin-api
            cpu: 1
            memory: 1Gi
            concurrency: 10
            min_instances: 0
            max_instances: 10
            allow_unauthenticated: true
    steps:
      - uses: actions/checkout@v3

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ env.GCP_SA_KEY }}

      - name: Build and push Docker image
        run: |
          cd services/${{ matrix.service }}
          gcloud builds submit --tag gcr.io/${{ env.PROJECT_ID }}/${{ matrix.service }}:${{ github.sha }}

      - name: Deploy to Cloud Run
        run: |
          AUTH_FLAG=${{ matrix.allow_unauthenticated == true && '--allow-unauthenticated' || '--no-allow-unauthenticated' }}
          TIMEOUT_FLAG=${{ matrix.timeout && format('--timeout {0}', matrix.timeout) || '' }}

          gcloud run deploy ${{ matrix.service }} \
            --image gcr.io/${{ env.PROJECT_ID }}/${{ matrix.service }}:${{ github.sha }} \
            --platform managed \
            --region ${{ env.REGION }} \
            $AUTH_FLAG \
            --memory ${{ matrix.memory }} \
            --cpu ${{ matrix.cpu }} \
            --concurrency ${{ matrix.concurrency }} \
            --min-instances ${{ matrix.min_instances }} \
            --max-instances ${{ matrix.max_instances }} \
            $TIMEOUT_FLAG

  smoke-test:
    needs: build-and-deploy
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ env.GCP_SA_KEY }}

      - name: Run smoke test
        run: |
          chmod +x scripts/smoke-test.sh
          ./scripts/smoke-test.sh
