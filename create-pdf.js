const fs = require('fs');
const PDFDocument = require('pdfkit');

// Create a document
const doc = new PDFDocument();

// Pipe its output somewhere, like to a file
doc.pipe(fs.createWriteStream('sample.pdf'));

// Read the text file
const text = fs.readFileSync('sample-text.txt', 'utf8');

// Add the text to the PDF
doc.fontSize(12);
doc.text(text, {
  paragraphGap: 10,
  indent: 20,
  align: 'justify',
  columns: 1
});

// Finalize the PDF and end the stream
doc.end();
