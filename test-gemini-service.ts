import { analyzeDocument } from './src/services/geminiService';
import * as fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Simple test for the Gemini service
 * This test loads the Airbnb PDF from the test folder and sends it to Gemini for analysis
 */
async function testGeminiService() {
  try {
    console.log('Starting Gemini service test...');

    // Path to the PDF file in the test folder
    const pdfPath = './test/Airbnb.pdf';

    if (!fs.existsSync(pdfPath)) {
      console.error(`Error: Test PDF file not found at ${pdfPath}`);
      return;
    }

    // Load the PDF file
    console.log(`Loading PDF from ${pdfPath}...`);
    const fileBuffer = fs.readFileSync(pdfPath);
    console.log(`PDF loaded successfully. Size: ${(fileBuffer.length / 1024).toFixed(2)} KB`);

    // Enhanced prompt for testing with cross-checking instructions
    const testPrompt = `
    Analyze this pitch deck with the following criteria:

    1. Team (25%): Evaluate the founding team's experience, domain expertise, and track record.
       - Cross-check team members' LinkedIn profiles and online presence
       - Research their previous projects, companies, and achievements
       - Verify educational backgrounds and professional credentials
       - Assess team completeness and identify any critical skill gaps

    2. Market (20%): Assess market size, growth potential, and competitive landscape.
       - Verify market size claims with latest industry reports and data
       - Research current market trends and growth projections
       - Identify key competitors and their market positions
       - Evaluate regulatory environment and potential challenges

    3. Product (20%): Evaluate product-market fit, innovation, and technical feasibility.
       - Research similar products/services in the market
       - Assess technological feasibility and implementation challenges
       - Evaluate intellectual property position and defensibility
       - Check for any recent technological advancements that might impact the product

    4. Traction (15%): Analyze current customers, revenue, and growth metrics.
       - Verify any customer or revenue claims with available public information
       - Research customer reviews, testimonials, or public feedback
       - Compare growth metrics with industry benchmarks
       - Check for any recent news about customer acquisitions or partnerships

    5. Business Model (10%): Evaluate revenue model, unit economics, and scalability.
       - Compare with similar business models in the industry
       - Assess pricing strategy against competitors
       - Evaluate scalability challenges and potential solutions
       - Research customer acquisition costs and lifetime value benchmarks

    6. Financials (10%): Assess financial projections, funding needs, and use of funds.
       - Check for any public information about funding rounds
       - Evaluate financial projections against industry standards
       - Research burn rate and runway estimates for similar companies
       - Assess valuation in context of comparable companies

    7. External Factors: Consider any external factors that might impact the business.
       - Research recent regulatory changes affecting the industry
       - Identify macroeconomic trends that could impact the business
       - Check for any recent news about the company or founders
       - Evaluate potential competitive threats or market disruptions

    Be critical but fair in your assessment. Cross-check all information in the pitch deck with publicly available sources. Highlight any discrepancies or concerns. Use the most up-to-date information available.
    `;

    console.log('Sending PDF to Gemini for analysis...');
    const startTime = Date.now();

    // Call the Gemini service
    const result = await analyzeDocument(fileBuffer, testPrompt);

    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000;

    console.log(`Analysis completed in ${processingTime.toFixed(2)} seconds`);
    console.log('\nAnalysis Results:');
    console.log('=================');
    console.log(`Company: ${result.companyName}`);
    console.log(`Score: ${result.score}/100`);
    console.log(`Summary: ${result.summary}`);

    console.log('\nStrengths:');
    result.strengths.forEach((strength, index) => {
      console.log(`${index + 1}. ${strength}`);
    });

    console.log('\nWeaknesses:');
    result.weaknesses.forEach((weakness, index) => {
      console.log(`${index + 1}. ${weakness}`);
    });

    console.log('\nMarket Opportunity:');
    console.log(`Size: ${result.marketOpportunity.size}`);
    console.log(`Growth: ${result.marketOpportunity.growth}`);
    console.log(`Assessment: ${result.marketOpportunity.assessment}`);

    console.log('\nTeam:');
    console.log(`Assessment: ${result.team.assessment}`);
    console.log(`Experience: ${result.team.experience}`);
    console.log(`Gaps: ${result.team.gaps}`);

    console.log('\nFinancials:');
    console.log(`Assessment: ${result.financials.assessment}`);
    console.log(`Runway: ${result.financials.runway}`);
    console.log(`Projections: ${result.financials.projections}`);

    console.log('\nInvestment Recommendation:');
    console.log(result.investmentRecommendation);

    console.log('\nFollow-up Questions:');
    result.followUpQuestions.forEach((question, index) => {
      console.log(`${index + 1}. ${question}`);
    });

    console.log('\nTest completed successfully!');

    // Save the result to a file for reference
    fs.writeFileSync('gemini-analysis-result.json', JSON.stringify(result, null, 2));
    console.log('Analysis result saved to gemini-analysis-result.json');

  } catch (error) {
    console.error('Error testing Gemini service:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testGeminiService();
