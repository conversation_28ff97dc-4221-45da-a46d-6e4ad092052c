const { initializeApp, applicationDefault } = require('firebase-admin/app');
const { getFirestore } = require('firebase-admin/firestore');

// Initialize Firebase Admin
initializeApp({
  credential: applicationDefault(),
  projectId: 'valurize-app',
});

const db = getFirestore();

async function createTenant() {
  try {
    const tenantRef = db.collection('TENANTS').doc('T08PGD0RT5X');
    await tenantRef.set({
      teamId: 'T08PGD0RT5X',
      slackConnected: true,
      slackBotUserId: 'U08P2LD6W4D',
      slackTeamName: 'Valurize',
      tier: 'free',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    console.log('Tenant created successfully');
  } catch (error) {
    console.error('Error creating tenant:', error);
  }
}

createTenant();
