const { initializeApp, applicationDefault } = require('firebase-admin/app');
const { getFirestore } = require('firebase-admin/firestore');

// Initialize Firebase
const app = initializeApp({
  credential: applicationDefault(),
});

const db = getFirestore();

// Get command line arguments
const batchId = process.argv[2];
const teamId = process.argv[3];

if (!batchId) {
  console.error('Usage: node delete-batch.js <batchId> [teamId]');
  process.exit(1);
}

async function deleteBatch() {
  try {
    // Delete batch record
    await db.collection('BATCHES').doc(batchId).delete();
    console.log(`Successfully deleted batch record with ID: ${batchId}`);
    
    // Delete tenant if provided
    if (teamId) {
      await db.collection('TENANTS').doc(teamId).delete();
      console.log(`Successfully deleted tenant with ID: ${teamId}`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error deleting records:', error);
    process.exit(1);
  }
}

deleteBatch();
