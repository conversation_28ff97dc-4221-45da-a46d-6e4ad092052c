const { initializeApp, applicationDefault } = require('firebase-admin/app');
const { getFirestore } = require('firebase-admin/firestore');

// Initialize Firebase
const app = initializeApp({
  credential: applicationDefault(),
});

const db = getFirestore();

// Get command line arguments
const batchId = process.argv[2];
const teamId = process.argv[3];
const gcsPath = process.argv[4];
const filename = process.argv[5] || 'test-pitch-deck.pdf';
const userEmail = process.argv[6] || '<EMAIL>';

if (!batchId || !teamId || !gcsPath) {
  console.error('Usage: node create-batch.js <batchId> <teamId> <gcsPath> [filename] [userEmail]');
  process.exit(1);
}

async function createBatch() {
  try {
    const now = new Date();
    
    // Create batch record
    await db.collection('BATCHES').doc(batchId).set({
      teamId: teamId,
      userId: userEmail,
      channelId: 'test-channel',
      filename: filename,
      gcsPath: gcsPath,
      status: 'pending',
      source: 'smoke-test',
      createdAt: now,
      updatedAt: now
    });
    
    console.log(`Successfully created batch record with ID: ${batchId}`);
    process.exit(0);
  } catch (error) {
    console.error('Error creating batch record:', error);
    process.exit(1);
  }
}

createBatch();
