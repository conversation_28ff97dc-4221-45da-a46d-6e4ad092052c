#!/bin/bash

# Deploy script for Valurize app (slack-ingest only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Slack Ingest to project: ${PROJECT_ID}, region: ${REGION}"

# Build shared package
echo "Building shared package..."
cd services/shared
npm install
npm run build
cd ../..

# Build and deploy services
echo "Building and deploying Slack Ingest..."

# Deploy Slack Integration
echo "Deploying Slack Integration..."
cd services/slack-ingest
npm install
gcloud builds submit --tag gcr.io/${PROJECT_ID}/slack-ingest
gcloud run deploy slack-ingest \
  --image gcr.io/${PROJECT_ID}/slack-ingest \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 50 \
  --min-instances 1 \
  --max-instances 100 \
  --set-env-vars="SLACK_SIGNING_SECRET=${SLACK_SIGNING_SECRET},SLACK_CLIENT_ID=${SLACK_CLIENT_ID},SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET},SLACK_STATE_SECRET=${SLACK_STATE_SECRET:-valurize-state-secret},GOOGLE_CLOUD_PROJECT=${PROJECT_ID},LOG_LEVEL=${LOG_LEVEL:-info},PDF_BUCKET_NAME=${PDF_BUCKET_NAME:-${PROJECT_ID}-pdfs},PDF_PROCESSING_TOPIC=${PDF_PROCESSING_TOPIC:-pdf-processing},SERVICE_URL=https://slack-ingest-${PROJECT_ID}.a.run.app,NODE_ENV=production"
cd ../..

echo "Deployment completed successfully!"
