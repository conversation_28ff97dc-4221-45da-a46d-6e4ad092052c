#!/bin/bash

# Deploy script for Valurize app (slack-notify only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Slack Notify to project: ${PROJECT_ID}, region: ${REGION}"

# Build and deploy services
echo "Building and deploying Slack Notify..."

# Deploy Slack Notify
echo "Deploying Slack Notify..."
cd services/slack-notify

# Build TypeScript files
echo "Building TypeScript files..."
npm install
npm run build

# Deploy to Cloud Run
gcloud builds submit --tag gcr.io/${PROJECT_ID}/slack-notify
gcloud run deploy slack-notify \
  --image gcr.io/${PROJECT_ID}/slack-notify \
  --platform managed \
  --region ${REGION} \
  --no-allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 10 \
  --min-instances 0 \
  --max-instances 20
cd ../..

echo "Deployment completed successfully!"
