#!/bin/bash

# Deploy script for Valurize app (processor only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying PDF Processor to project: ${PROJECT_ID}, region: ${REGION}"

# Build and deploy services
echo "Building and deploying PDF Processor..."

# Deploy PDF Processor
echo "Deploying PDF Processor..."
cd services/processor

# Build TypeScript files
echo "Building TypeScript files..."
npm install
npm run build

# Deploy to Cloud Run
gcloud builds submit --tag gcr.io/${PROJECT_ID}/processor
gcloud run deploy processor \
  --image gcr.io/${PROJECT_ID}/processor \
  --platform managed \
  --region ${REGION} \
  --no-allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --concurrency 1 \
  --min-instances 1 \
  --max-instances 100 \
  --timeout 900s \
  --set-env-vars="VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION},VERTEX_AI_PROJECT_ID=${VERTEX_AI_PROJECT_ID}"
cd ../..

echo "Deployment completed successfully!"
