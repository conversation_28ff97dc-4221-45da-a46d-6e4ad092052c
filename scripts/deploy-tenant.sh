#!/bin/bash

# Deploy script for Valurize app (tenant-api only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Tenant API to project: ${PROJECT_ID}, region: ${REGION}"

# Apply Terraform configuration
echo "Applying Terraform configuration..."
cd infra
terraform apply -var="project_id=${PROJECT_ID}" -var="region=${REGION}" -auto-approve
cd ..

# Build and deploy services
echo "Building and deploying Tenant API..."

# Deploy Tenant API
echo "Deploying Tenant API..."
cd services/tenant
gcloud builds submit --tag gcr.io/${PROJECT_ID}/tenant-api
gcloud run deploy tenant-api \
  --image gcr.io/${PROJECT_ID}/tenant-api \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --concurrency 10 \
  --min-instances 1 \
  --max-instances 20
cd ../..

echo "Deployment completed successfully!"
