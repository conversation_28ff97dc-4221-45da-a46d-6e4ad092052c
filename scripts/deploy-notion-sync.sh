#!/bin/bash

# Deploy script for Valurize app (notion-sync only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Notion Sync to project: ${PROJECT_ID}, region: ${REGION}"

# Build and deploy services
echo "Building and deploying Notion Sync..."

# Deploy Notion Sync
echo "Deploying Notion Sync..."
cd services/notion-sync

# Build TypeScript files
echo "Building TypeScript files..."
npm install
npm run build

# Deploy to Cloud Run
gcloud builds submit --tag gcr.io/${PROJECT_ID}/notion-sync
gcloud run deploy notion-sync \
  --image gcr.io/${PROJECT_ID}/notion-sync \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 10 \
  --min-instances 0 \
  --max-instances 20 \
  --set-env-vars="NOTION_CLIENT_ID=${NOTION_CLIENT_ID},NOTION_CLIENT_SECRET=${NOTION_CLIENT_SECRET},SERVICE_URL=${SERVICE_URL},FRONTEND_URL=${FRONTEND_URL}"
cd ../..

echo "Deployment completed successfully!"
