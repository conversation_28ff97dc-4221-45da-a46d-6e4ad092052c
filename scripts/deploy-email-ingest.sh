#!/bin/bash

# Deploy script for Valurize app (email-ingest only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Email Ingest to project: ${PROJECT_ID}, region: ${REGION}"

# Build and deploy services
echo "Building and deploying Email Ingest..."

# Deploy Email Integration
echo "Deploying Email Integration..."
cd services/email-ingest

# Build TypeScript files
echo "Building TypeScript files..."
npm install
npm run build

# Deploy to Cloud Run
gcloud builds submit --tag gcr.io/${PROJECT_ID}/email-ingest
gcloud run deploy email-ingest \
  --image gcr.io/${PROJECT_ID}/email-ingest \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 50 \
  --min-instances 1 \
  --max-instances 100 \
  --set-env-vars="MAILGUN_API_KEY=${MAILGUN_API_KEY},MAILGUN_DOMAIN=${MAILGUN_DOMAIN}"
cd ../..

echo "Deployment completed successfully!"
