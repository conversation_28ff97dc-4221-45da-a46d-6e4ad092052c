#!/bin/bash

# Deploy script for Valurize app

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Valurize app to project: ${PROJECT_ID}, region: ${REGION}"

# Apply Terraform configuration
echo "Applying Terraform configuration..."
cd infra
terraform apply -var="project_id=${PROJECT_ID}" -var="region=${REGION}" -auto-approve
cd ..

# Build and deploy services
echo "Building and deploying services..."

# Deploy Tenant API
echo "Deploying Tenant API..."
cd services/tenant
gcloud builds submit --tag gcr.io/${PROJECT_ID}/tenant-api
gcloud run deploy tenant-api \
  --image gcr.io/${PROJECT_ID}/tenant-api \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --concurrency 10 \
  --min-instances 1 \
  --max-instances 20
cd ../..

# Deploy Slack Integration
echo "Deploying Slack Integration..."
cd services/slack-ingest
gcloud builds submit --tag gcr.io/${PROJECT_ID}/slack-ingest
gcloud run deploy slack-ingest \
  --image gcr.io/${PROJECT_ID}/slack-ingest \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 50 \
  --min-instances 1 \
  --max-instances 100
cd ../..

# Deploy Email Integration
echo "Deploying Email Integration..."
cd services/email-ingest
gcloud builds submit --tag gcr.io/${PROJECT_ID}/email-ingest
gcloud run deploy email-ingest \
  --image gcr.io/${PROJECT_ID}/email-ingest \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 1 \
  --concurrency 50 \
  --min-instances 1 \
  --max-instances 100
cd ../..

# Deploy PDF Processor
echo "Deploying PDF Processor..."
cd services/processor
gcloud builds submit --tag gcr.io/${PROJECT_ID}/processor
gcloud run deploy processor \
  --image gcr.io/${PROJECT_ID}/processor \
  --platform managed \
  --region ${REGION} \
  --no-allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --concurrency 1 \
  --min-instances 1 \
  --max-instances 100 \
  --timeout 900s
cd ../..

# Deploy Slack Notify
echo "Deploying Slack Notify..."
cd services/slack-notify
gcloud builds submit --tag gcr.io/${PROJECT_ID}/slack-notify
gcloud run deploy slack-notify \
  --image gcr.io/${PROJECT_ID}/slack-notify \
  --platform managed \
  --region ${REGION} \
  --no-allow-unauthenticated \
  --memory 512Mi \
  --cpu 0.5 \
  --concurrency 10 \
  --min-instances 0 \
  --max-instances 20
cd ../..

# Deploy Notion Sync
echo "Deploying Notion Sync..."
cd services/notion-sync
gcloud builds submit --tag gcr.io/${PROJECT_ID}/notion-sync
gcloud run deploy notion-sync \
  --image gcr.io/${PROJECT_ID}/notion-sync \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 512Mi \
  --cpu 0.5 \
  --concurrency 10 \
  --min-instances 0 \
  --max-instances 20
cd ../..

# Deploy Admin API
echo "Deploying Admin API..."
cd services/admin-api
gcloud builds submit --tag gcr.io/${PROJECT_ID}/admin-api
gcloud run deploy admin-api \
  --image gcr.io/${PROJECT_ID}/admin-api \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --concurrency 10 \
  --min-instances 0 \
  --max-instances 10
cd ../..

# Deploy Token Rotator
echo "Deploying Token Rotator..."
cd services/token-rotator
gcloud builds submit --tag gcr.io/${PROJECT_ID}/token-rotator
gcloud run deploy token-rotator \
  --image gcr.io/${PROJECT_ID}/token-rotator \
  --platform managed \
  --region ${REGION} \
  --no-allow-unauthenticated \
  --memory 128Mi \
  --cpu 0.2 \
  --concurrency 1 \
  --min-instances 0 \
  --max-instances 1
cd ../..

echo "Deployment completed successfully!"
