#!/bin/bash

# Deploy script for Valurize app (token-rotator only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Token Rotator to project: ${PROJECT_ID}, region: ${REGION}"

# Build shared package
echo "Building shared package..."
cd services/shared
npm install
npm run build
cd ../..

# Build and deploy services
echo "Building and deploying Token Rotator..."

# Deploy Token Rotator
echo "Deploying Token Rotator..."
cd services/token-rotator
npm install
gcloud builds submit --tag gcr.io/${PROJECT_ID}/token-rotator
gcloud run deploy token-rotator \
  --image gcr.io/${PROJECT_ID}/token-rotator \
  --platform managed \
  --region ${REGION} \
  --no-allow-unauthenticated \
  --memory 256Mi \
  --cpu 0.2 \
  --concurrency 1 \
  --min-instances 0 \
  --max-instances 1 \
  --set-env-vars="SLACK_CLIENT_ID=${SLACK_CLIENT_ID},SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET},SLACK_STATE_SECRET=${SLACK_STATE_SECRET:-valurize-state-secret},GOOGLE_CLOUD_PROJECT=${PROJECT_ID},LOG_LEVEL=${LOG_LEVEL:-info},TOKEN_ROTATE_SUBSCRIPTION=${TOKEN_ROTATE_SUBSCRIPTION:-token-rotate-subscription},NOTION_CLIENT_ID=${NOTION_CLIENT_ID},NOTION_CLIENT_SECRET=${NOTION_CLIENT_SECRET},NODE_ENV=production"
cd ../..

echo "Deployment completed successfully!"
