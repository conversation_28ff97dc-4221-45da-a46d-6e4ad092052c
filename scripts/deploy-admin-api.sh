#!/bin/bash

# Deploy script for Valurize app (admin-api only)

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"valurize-app"}
REGION=${REGION:-"us-central1"}

echo "Deploying Admin API to project: ${PROJECT_ID}, region: ${REGION}"

# Build and deploy services
echo "Building and deploying Admin API..."

# Deploy Admin API
echo "Deploying Admin API..."
cd services/admin-api

# Build TypeScript files
echo "Building TypeScript files..."
npm install
npm run build

# Deploy to Cloud Run
gcloud builds submit --tag gcr.io/${PROJECT_ID}/admin-api
gcloud run deploy admin-api \
  --image gcr.io/${PROJECT_ID}/admin-api \
  --platform managed \
  --region ${REGION} \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --concurrency 10 \
  --min-instances 0 \
  --max-instances 10
cd ../..

echo "Deployment completed successfully!"
