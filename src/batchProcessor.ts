import { getFirestore } from 'firebase-admin/firestore';
import { PubSub } from '@google-cloud/pubsub';
import { logger } from './utils/logger';

// Initialize Firestore
const db = getFirestore();

// Initialize PubSub
const pubsub = new PubSub();
const pdfProcessingTopic = pubsub.topic(process.env.PDF_PROCESSING_TOPIC || 'pdf-processing');

/**
 * Process pending user batches
 * This function is meant to be called by a Cloud Scheduler job
 */
export async function processPendingBatches() {
  try {
    // Get all pending user batches
    const batchesSnapshot = await db.collection('BATCHES')
      .where('status', '==', 'pending')
      .get();

    if (batchesSnapshot.empty) {
      logger.info('No pending batches to process');
      return;
    }

    logger.info(`Found ${batchesSnapshot.size} pending batches to process`);

    // Process each batch
    const promises = batchesSnapshot.docs.map(async (doc) => {
      const batch = doc.data();
      const { teamId, userId, pdfList } = batch;

      if (!pdfList || pdfList.length === 0) {
        logger.warn(`Empty PDF list for batch ${doc.id}`);
        return;
      }

      // Update batch status
      await doc.ref.update({
        status: 'processing',
        updatedAt: new Date(),
      });

      // Publish message to PDF processing topic
      await pdfProcessingTopic.publish(Buffer.from(JSON.stringify({
        teamId,
        userId,
        pdfList,
      })));

      logger.info(`Published batch ${doc.id} with ${pdfList.length} PDFs for processing`);
    });

    await Promise.all(promises);

    logger.info('Successfully processed all pending batches');
  } catch (error) {
    logger.error(`Error processing pending batches: ${error}`);
    throw error;
  }
}
