import axios from 'axios';
import { logger } from './logger';

/**
 * Downloads a file from a URL using the provided token for authentication
 * 
 * @param url The URL to download the file from
 * @param token The authentication token
 * @returns A Buffer containing the file data
 */
export async function downloadFile(url: string, token: string): Promise<Buffer> {
  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      responseType: 'arraybuffer',
    });
    
    return Buffer.from(response.data);
  } catch (error) {
    logger.error(`Error downloading file from ${url}: ${error}`);
    throw new Error(`Failed to download file: ${error}`);
  }
}
