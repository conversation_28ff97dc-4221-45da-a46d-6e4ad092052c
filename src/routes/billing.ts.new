import { Router } from 'express';
import { db, FieldValue } from '../index';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = Router();

// Get billing information for a tenant
router.get('/:teamId', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    
    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();
    
    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }

    const tenant = tenantDoc.data();
    if (!tenant) {
      return next(new AppError('Tenant data not found', 404));
    }

    // Calculate current billing cycle
    const joinDate = tenant.joinDate.toDate();
    const now = new Date();
    
    // Calculate cycle start and end dates
    const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    // Get usage for current cycle
    const cycleKey = `${teamId}_${cycleStart.getFullYear()}_${cycleStart.getMonth() + 1}`;
    const usageDoc = await db.collection('USAGE').doc(cycleKey).get();
    
    const usage = usageDoc.exists ? usageDoc.data()?.count || 0 : 0;
    const tierLimit = tenant.tier === 'free' ? 10 : 
                     tenant.tier === 'pro' ? 100 : 
                     tenant.tier === 'enterprise' ? 1000 : 0;
    
    // Calculate remaining quota
    const remaining = Math.max(0, tierLimit - usage);
    
    res.status(200).json({
      status: 'success',
      data: {
        tier: tenant.tier,
        cycleStart: cycleStart.toISOString(),
        cycleEnd: cycleEnd.toISOString(),
        usage,
        limit: tierLimit,
        remaining,
      },
    });
  } catch (error) {
    logger.error(`Error getting billing info: ${error}`);
    next(error);
  }
});

// Add extra quota for a tenant
router.post('/:teamId/add-extra', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    const { amount } = req.body;
    
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return next(new AppError('Invalid amount', 400));
    }
    
    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();
    
    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }
    
    // Calculate current billing cycle
    const now = new Date();
    
    // Update tenant with extra quota
    await db.collection('TENANTS').doc(teamId).update({
      extraQuota: FieldValue.increment(amount),
      updatedAt: now,
    });
    
    res.status(200).json({
      status: 'success',
      message: `Added ${amount} extra quota for tenant ${teamId}`,
    });
  } catch (error) {
    logger.error(`Error adding extra quota: ${error}`);
    next(error);
  }
});

export default router;
