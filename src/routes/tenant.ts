import { Router } from 'express';
import { db } from '../index';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import Jo<PERSON> from 'joi';

const router = Router();
const secretManager = new SecretManagerServiceClient();

// Validation schema for tenant creation/update
const tenantSchema = Joi.object({
  tier: Joi.string().valid('free', 'pro', 'enterprise').required(),
  prompt: Joi.string().max(1000),
  thresholds: Joi.object({
    notionSync: Joi.number().min(0).max(100).default(70),
  }),
  slackToken: Joi.string(),
  notionToken: Joi.string(),
});

// Get tenant by ID
router.get('/:teamId', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }

    const tenant = tenantDoc.data();
    
    // Remove sensitive information
    if (tenant) {
      delete tenant.slackToken;
      delete tenant.notionToken;
    }

    res.status(200).json({
      status: 'success',
      data: tenant,
    });
  } catch (error) {
    logger.error(`Error getting tenant: ${error}`);
    next(error);
  }
});

// Create or update tenant
router.post('/:teamId', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    const { error, value } = tenantSchema.validate(req.body);

    if (error) {
      return next(new AppError(`Invalid request: ${error.message}`, 400));
    }

    const tenantData = value;
    const now = new Date();

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();
    const isNewTenant = !tenantDoc.exists;

    // Set join date for new tenants
    if (isNewTenant) {
      tenantData.joinDate = now;
    }

    // Handle Slack token if provided
    if (tenantData.slackToken) {
      const secretName = `slack-token-${teamId}`;
      await createOrUpdateSecret(secretName, tenantData.slackToken);
      delete tenantData.slackToken;
    }

    // Handle Notion token if provided
    if (tenantData.notionToken) {
      const secretName = `notion-token-${teamId}`;
      await createOrUpdateSecret(secretName, tenantData.notionToken);
      delete tenantData.notionToken;
    }

    // Update tenant in Firestore
    await db.collection('TENANTS').doc(teamId).set(
      {
        ...tenantData,
        updatedAt: now,
      },
      { merge: true }
    );

    // Initialize usage record if new tenant
    if (isNewTenant) {
      const cycleKey = `${teamId}_${now.getFullYear()}_${now.getMonth() + 1}`;
      await db.collection('USAGE').doc(cycleKey).set({
        count: 0,
        lastUpdated: now,
      });
    }

    res.status(isNewTenant ? 201 : 200).json({
      status: 'success',
      message: isNewTenant ? 'Tenant created successfully' : 'Tenant updated successfully',
    });
  } catch (error) {
    logger.error(`Error creating/updating tenant: ${error}`);
    next(error);
  }
});

// Delete tenant
router.delete('/:teamId', async (req, res, next) => {
  try {
    const { teamId } = req.params;
    
    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();
    
    if (!tenantDoc.exists) {
      return next(new AppError('Tenant not found', 404));
    }

    // Delete tenant from Firestore
    await db.collection('TENANTS').doc(teamId).delete();

    // Delete secrets
    try {
      await deleteSecret(`slack-token-${teamId}`);
      await deleteSecret(`notion-token-${teamId}`);
    } catch (error) {
      logger.warn(`Error deleting secrets for tenant ${teamId}: ${error}`);
    }

    res.status(200).json({
      status: 'success',
      message: 'Tenant deleted successfully',
    });
  } catch (error) {
    logger.error(`Error deleting tenant: ${error}`);
    next(error);
  }
});

// Helper function to create or update a secret
async function createOrUpdateSecret(secretName: string, secretValue: string) {
  const projectId = process.env.GOOGLE_CLOUD_PROJECT;
  const parent = `projects/${projectId}`;
  const secretPath = `${parent}/secrets/${secretName}`;

  try {
    // Check if secret exists
    try {
      await secretManager.getSecret({ name: secretPath });
    } catch (error) {
      // Create secret if it doesn't exist
      await secretManager.createSecret({
        parent,
        secretId: secretName,
        secret: {
          replication: {
            automatic: {},
          },
        },
      });
    }

    // Add new version
    await secretManager.addSecretVersion({
      parent: secretPath,
      payload: {
        data: Buffer.from(secretValue),
      },
    });
  } catch (error) {
    logger.error(`Error managing secret ${secretName}: ${error}`);
    throw error;
  }
}

// Helper function to delete a secret
async function deleteSecret(secretName: string) {
  const projectId = process.env.GOOGLE_CLOUD_PROJECT;
  const secretPath = `projects/${projectId}/secrets/${secretName}`;

  try {
    await secretManager.deleteSecret({ name: secretPath });
  } catch (error) {
    logger.error(`Error deleting secret ${secretName}: ${error}`);
    throw error;
  }
}

export default router;
