import { App, ExpressReceiver } from '@slack/bolt';
import { WebClient } from '@slack/web-api';
import express from 'express';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { Storage } from '@google-cloud/storage';
import { PubSub } from '@google-cloud/pubsub';
import { v4 as uuidv4 } from 'uuid';
import { logger } from './utils/logger';
import { downloadFile } from './utils/fileDownloader';

// Load environment variables
dotenv.config();

// Initialize Firebase Admin
initializeApp({
  credential: cert({
    projectId: process.env.GOOGLE_CLOUD_PROJECT,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  }),
});

// Initialize Firestore
const db = getFirestore();

// Initialize Secret Manager
const secretManager = new SecretManagerServiceClient();

// Initialize Storage
const storage = new Storage();
const bucketName = process.env.PDF_BUCKET_NAME || `${process.env.GOOGLE_CLOUD_PROJECT}-pdfs`;
const bucket = storage.bucket(bucketName);

// Initialize PubSub
const pubsub = new PubSub();
const pdfProcessingTopic = pubsub.topic(process.env.PDF_PROCESSING_TOPIC || 'pdf-processing');

// Create Express receiver
const receiver = new ExpressReceiver({
  signingSecret: process.env.SLACK_SIGNING_SECRET || '',
  processBeforeResponse: true,
});

// Create Slack app
const app = new App({
  receiver,
  token: process.env.SLACK_BOT_TOKEN,
  processBeforeResponse: true,
});

// Add middleware to Express
receiver.app.use(helmet());
receiver.app.use(express.json());

// Health check endpoint
receiver.app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Endpoint to process pending batches
receiver.app.post('/process-batches', async (req, res) => {
  try {
    const { processPendingBatches } = require('./batchProcessor');
    await processPendingBatches();
    res.status(200).json({ status: 'success', message: 'Batch processing triggered' });
  } catch (error) {
    logger.error(`Error triggering batch processing: ${error}`);
    res.status(500).json({ status: 'error', message: 'Failed to trigger batch processing' });
  }
});

// Command: /uploaddeck
app.command('/uploaddeck', async ({ command, ack, respond }) => {
  await ack();

  try {
    const teamId = command.team_id;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      await respond({
        text: 'Your team is not registered with Valurize. Please contact support.',
      });
      return;
    }

    await respond({
      text: 'Please upload your pitch deck PDF. I\'ll analyze it for you!',
    });
  } catch (error) {
    logger.error(`Error handling /uploaddeck command: ${error}`);
    await respond({
      text: 'Sorry, something went wrong. Please try again later.',
    });
  }
});

// Command: /topdecks
app.command('/topdecks', async ({ command, ack, respond }) => {
  await ack();

  try {
    const teamId = command.team_id;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      await respond({
        text: 'Your team is not registered with Valurize. Please contact support.',
      });
      return;
    }

    // Query top decks from Firestore
    const batchesSnapshot = await db.collection('BATCHES')
      .where('teamId', '==', teamId)
      .where('status', '==', 'completed')
      .orderBy('score', 'desc')
      .limit(5)
      .get();

    if (batchesSnapshot.empty) {
      await respond({
        text: 'No analyzed pitch decks found. Upload some decks to get started!',
      });
      return;
    }

    // Format response
    const blocks = [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Top Pitch Decks*',
        },
      },
      {
        type: 'divider',
      },
    ];

    batchesSnapshot.forEach((doc) => {
      const batch = doc.data();
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*${batch.companyName || 'Unnamed Company'}*\nScore: ${batch.score}/100\nAnalyzed: ${new Date(batch.completedAt).toLocaleDateString()}`,
        },
      });
    });

    await respond({
      blocks,
    });
  } catch (error) {
    logger.error(`Error handling /topdecks command: ${error}`);
    await respond({
      text: 'Sorry, something went wrong. Please try again later.',
    });
  }
});

// Command: /connect-notion
app.command('/connect-notion', async ({ command, ack, respond }) => {
  await ack();

  try {
    const teamId = command.team_id;

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      await respond({
        text: 'Your team is not registered with Valurize. Please contact support.',
      });
      return;
    }

    // Generate OAuth URL
    const notionClientId = process.env.NOTION_CLIENT_ID;
    const redirectUri = `${process.env.SERVICE_URL}/notion/callback`;
    const state = teamId;

    const authUrl = `https://api.notion.com/v1/oauth/authorize?client_id=${notionClientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&state=${state}`;

    await respond({
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Connect Valurize to your Notion workspace to automatically sync pitch deck analyses.',
          },
        },
        {
          type: 'actions',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Connect to Notion',
                emoji: true,
              },
              url: authUrl,
            },
          ],
        },
      ],
    });
  } catch (error) {
    logger.error(`Error handling /connect-notion command: ${error}`);
    await respond({
      text: 'Sorry, something went wrong. Please try again later.',
    });
  }
});

// Handle file_shared events
app.event('file_shared', async ({ event, context, client }) => {
  try {
    const fileId = event.file_id;
    const teamId = context.teamId || '';

    if (!teamId) {
      logger.error('No team ID found in context');
      return;
    }

    // Check if tenant exists
    const tenantDoc = await db.collection('TENANTS').doc(teamId).get();

    if (!tenantDoc.exists) {
      logger.warn(`File shared by unregistered team: ${teamId}`);
      return;
    }

    // Get file info
    const fileInfo = await client.files.info({
      file: fileId,
    });

    if (!fileInfo.file) {
      logger.error(`Could not get file info for file: ${fileId}`);
      return;
    }

    const file = fileInfo.file;

    // Check if file is a PDF
    if (file.mimetype !== 'application/pdf') {
      await client.chat.postMessage({
        channel: event.channel_id,
        text: 'Sorry, I can only analyze PDF files. Please upload a PDF pitch deck.',
      });
      return;
    }

    // Download file from Slack
    if (!file.url_private_download) {
      logger.error(`No download URL for file: ${fileId}`);
      return;
    }

    // Get Slack token for the team
    const slackToken = await getTeamSlackToken(teamId);
    if (!slackToken) {
      logger.error(`No Slack token found for team: ${teamId}`);
      return;
    }

    // Create a new Slack client with the team's token
    const teamClient = new WebClient(slackToken);

    // Download the file
    const fileBuffer = await downloadFile(file.url_private_download, slackToken);

    // Generate a unique filename
    const uuid = uuidv4();
    const filename = `uploads/${teamId}/${uuid}.pdf`;

    // Upload to GCS
    await bucket.file(filename).save(fileBuffer, {
      metadata: {
        contentType: 'application/pdf',
        metadata: {
          teamId,
          originalFilename: file.name,
          fileId,
          channelId: event.channel_id,
        },
      },
    });

    // Create a batch record in Firestore
    const batchId = uuid;
    const now = new Date();

    await db.collection('BATCHES').doc(batchId).set({
      teamId,
      fileId,
      channelId: event.channel_id,
      filename: file.name,
      gcsPath: filename,
      status: 'pending',
      createdAt: now,
      updatedAt: now,
    });

    // Group PDFs by user for batch processing
    // Check if there's an existing batch for this user
    const userBatchKey = `${teamId}_${context.userId || 'unknown'}`;
    const userBatchRef = db.collection('USER_BATCHES').doc(userBatchKey);

    await db.runTransaction(async (transaction) => {
      const userBatchDoc = await transaction.get(userBatchRef);

      if (!userBatchDoc.exists) {
        // Create a new batch for this user
        transaction.set(userBatchRef, {
          teamId,
          userId: context.userId || 'unknown',
          pdfList: [batchId],
          status: 'pending',
          createdAt: now,
          updatedAt: now,
        });
      } else {
        // Add to existing batch
        const userBatch = userBatchDoc.data();
        if (userBatch) {
          const pdfList = userBatch.pdfList || [];
          pdfList.push(batchId);

          transaction.update(userBatchRef, {
            pdfList,
            updatedAt: now,
          });
        }
      }
    });

    // Publish message to PDF processing topic with the entire batch
    // We'll use a debounce mechanism to wait for multiple uploads
    // For now, we'll publish immediately for demonstration
    await pdfProcessingTopic.publish(Buffer.from(JSON.stringify({
      teamId,
      userId: context.userId || 'unknown',
      pdfList: [batchId],
    })));

    // Notify user
    await teamClient.chat.postMessage({
      channel: event.channel_id,
      text: `I've received your pitch deck "${file.name}" and I'm analyzing it now. I'll notify you when it's ready!`,
    });

    logger.info(`File ${fileId} from team ${teamId} queued for processing with batch ID ${batchId}`);
  } catch (error) {
    logger.error(`Error handling file_shared event: ${error}`);
  }
});

// Helper function to get Slack token for a team
async function getTeamSlackToken(teamId: string): Promise<string | null> {
  try {
    const secretName = `projects/${process.env.GOOGLE_CLOUD_PROJECT}/secrets/slack-token-${teamId}/versions/latest`;
    const [version] = await secretManager.accessSecretVersion({ name: secretName });

    if (!version.payload || !version.payload.data) {
      return null;
    }

    return version.payload.data.toString();
  } catch (error) {
    logger.error(`Error getting Slack token for team ${teamId}: ${error}`);
    return null;
  }
}

// Start the server
const port = process.env.PORT || 8080;
(async () => {
  await app.start(port);
  logger.info(`Slack integration service running on port ${port}`);
})();

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await app.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await app.stop();
  process.exit(0);
});
