# Deployment Instructions for Token Rotation Fix

## Summary of Changes

We've made the following critical changes to fix the token rotation logic:

1. **Updated Token Rotation Logic in `services/token-rotator/src/services/slackTokenService.ts`**:
   - Changed the token expiration check to use the Cloud Scheduler interval (11 hours) instead of a fixed 1-hour window
   - This ensures tokens are always refreshed before they expire, even if a scheduled run fails
   - The fix prevents the scenario where a token could expire between scheduled checks

2. **Fixed TypeScript Issues in Shared Utilities**:
   - Updated type definitions in `services/shared/src/utils/secretManagerInstallationStore.ts`
   - Fixed access_token property access in `services/shared/src/utils/slackClientFactory.ts`

## Deployment Steps

To deploy these changes:

1. **Build and Deploy the Shared Package**:
   ```bash
   cd services/shared
   npm install
   npm run build
   cd ../..
   ```

2. **Deploy the Token Rotator Service**:
   ```bash
   cd services/token-rotator
   npm install
   npm run build
   gcloud builds submit --tag gcr.io/[PROJECT_ID]/token-rotator
   gcloud run deploy token-rotator \
     --image gcr.io/[PROJECT_ID]/token-rotator \
     --platform managed \
     --region [REGION] \
     --no-allow-unauthenticated \
     --memory 256Mi \
     --cpu 0.2 \
     --concurrency 1 \
     --min-instances 0 \
     --max-instances 1 \
     --set-env-vars="SLACK_CLIENT_ID=[SLACK_CLIENT_ID],SLACK_CLIENT_SECRET=[SLACK_CLIENT_SECRET],SLACK_STATE_SECRET=[SLACK_STATE_SECRET],GOOGLE_CLOUD_PROJECT=[PROJECT_ID],LOG_LEVEL=info,TOKEN_ROTATE_SUBSCRIPTION=token-rotate-subscription,NOTION_CLIENT_ID=[NOTION_CLIENT_ID],NOTION_CLIENT_SECRET=[NOTION_CLIENT_SECRET],NODE_ENV=production"
   cd ../..
   ```

3. **Deploy the Slack Ingest Service**:
   ```bash
   cd services/slack-ingest
   npm install
   npm run build
   gcloud builds submit --tag gcr.io/[PROJECT_ID]/slack-ingest
   gcloud run deploy slack-ingest \
     --image gcr.io/[PROJECT_ID]/slack-ingest \
     --platform managed \
     --region [REGION] \
     --allow-unauthenticated \
     --memory 512Mi \
     --cpu 1 \
     --concurrency 50 \
     --min-instances 1 \
     --max-instances 100 \
     --set-env-vars="SLACK_BOT_TOKEN=[SLACK_BOT_TOKEN],SLACK_SIGNING_SECRET=[SLACK_SIGNING_SECRET],SLACK_CLIENT_ID=[SLACK_CLIENT_ID],SLACK_CLIENT_SECRET=[SLACK_CLIENT_SECRET],SLACK_STATE_SECRET=[SLACK_STATE_SECRET],GOOGLE_CLOUD_PROJECT=[PROJECT_ID],LOG_LEVEL=info,PDF_BUCKET_NAME=[PROJECT_ID]-pdfs,PDF_PROCESSING_TOPIC=pdf-processing,SERVICE_URL=https://slack-ingest-[PROJECT_ID].a.run.app,NODE_ENV=production"
   cd ../..
   ```

Replace all placeholders in square brackets with your actual values.

## Verification

After deployment, verify the fix by:

1. Check the logs of the token-rotator service after it runs to confirm it's using the new logic
2. Monitor for any token expiration issues in your application logs
3. Verify that tokens are being refreshed proactively before they expire

The updated logic ensures that tokens are refreshed when they have less than 11 hours of validity remaining, which aligns with your Cloud Scheduler configuration that runs every 11 hours.
