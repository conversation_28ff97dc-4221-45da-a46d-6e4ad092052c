apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: processor
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "100"  # Maximum number of instances
        autoscaling.knative.dev/minScale: "1"    # Minimum number of instances
    spec:
      containerConcurrency: 1  # Process one request at a time
      timeoutSeconds: 900      # 15 minutes timeout
      containers:
      - image: gcr.io/valurize-app/processor:latest
        resources:
          limits:
            memory: "2Gi"      # 2GB memory
            cpu: "2"           # 2 CPU
        env:
        - name: NODE_ENV
          value: "production"
        - name: GOOGLE_CLOUD_PROJECT
          value: "valurize-app"
        - name: PDF_BUCKET_NAME
          value: "valurize-app-pdfs"
        - name: RESULTS_BUCKET_NAME
          value: "valurize-app-results"
        - name: PDF_PROCESSING_TOPIC
          value: "pdf-processing"
        - name: PDF_PROCESSING_SUBSCRIPTION
          value: "pdf-processing-subscription"
        - name: ANALYSIS_COMPLETE_TOPIC
          value: "analysis-complete"
        - name: VERTEX_AI_PROJECT
          value: "valurize-app"
        - name: VERTEX_AI_REGION
          value: "us-central1"
        - name: DELETE_ORIGINAL_PDF
          value: "false"
