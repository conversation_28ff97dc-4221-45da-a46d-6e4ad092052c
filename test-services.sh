#!/bin/bash
# Smoke test script for Valurize services
# This script tests the public endpoints of the Valurize services

set -e

echo "=== Valurize Services Smoke Test ==="
echo "Testing public endpoints of Valurize services..."
echo ""

# Get service URLs
ADMIN_API_URL=$(gcloud run services describe admin-api --region us-central1 --format="value(status.url)")
EMAIL_INGEST_URL=$(gcloud run services describe email-ingest --region us-central1 --format="value(status.url)")
NOTION_SYNC_URL=$(gcloud run services describe notion-sync --region us-central1 --format="value(status.url)")
SLACK_INGEST_URL=$(gcloud run services describe slack-ingest --region us-central1 --format="value(status.url)")
TENANT_API_URL=$(gcloud run services describe tenant-api --region us-central1 --format="value(status.url)")
TOKEN_ROTATOR_URL=$(gcloud run services describe token-rotator --region us-central1 --format="value(status.url)")

# Test health endpoints for public services
echo "Testing health endpoints for public services..."
for SERVICE_URL in "${ADMIN_API_URL}" "${EMAIL_INGEST_URL}" "${NOTION_SYNC_URL}" "${SLACK_INGEST_URL}" "${TENANT_API_URL}" "${TOKEN_ROTATOR_URL}"; do
  SERVICE_NAME=$(echo "${SERVICE_URL}" | sed -E 's|https://([^-]+)-.*|\1|')
  echo -n "  ${SERVICE_NAME}: "
  RESPONSE=$(curl -s "${SERVICE_URL}/health")
  if [[ "${RESPONSE}" == *"status"*"ok"* ]]; then
    echo "✅ OK"
  else
    echo "❌ FAILED"
    echo "    Response: ${RESPONSE}"
  fi
done
echo ""

# Test admin API endpoints
echo "Testing admin API endpoints..."

# Test tenants endpoint
echo -n "  GET /api/tenants: "
RESPONSE=$(curl -s "${ADMIN_API_URL}/api/tenants")
if [[ "${RESPONSE}" == *"tenants"* ]] || [[ "${RESPONSE}" == "[]" ]]; then
  echo "✅ OK"
else
  echo "❌ FAILED"
  echo "    Response: ${RESPONSE}"
fi

# Test tenant API endpoints
echo "Testing tenant API endpoints..."

# Test tenants endpoint
echo -n "  GET /tenants: "
RESPONSE=$(curl -s "${TENANT_API_URL}/tenants")
if [[ "${RESPONSE}" == *"tenants"* ]] || [[ "${RESPONSE}" == "[]" ]]; then
  echo "✅ OK"
else
  echo "❌ FAILED"
  echo "    Response: ${RESPONSE}"
fi

echo ""
echo "=== Testing admin dashboard ==="
echo "Opening admin dashboard in browser..."
echo "Please check if the admin dashboard loads correctly at: https://valurize.app/admin"
echo ""

echo "=== Smoke Test Complete ==="
